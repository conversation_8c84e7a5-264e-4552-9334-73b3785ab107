import { Tooltip } from "@mui/material";
import CustomChip from "src/@core/components/mui/chip";
import { useContext } from "react";
import { AuthContext } from "src/context/AuthContext";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const Columns = () => {

    const { user } = useContext(AuthContext);
  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  return [
    { field: "name", headerName: "Role", flex: 1, minWidth: 120,
      renderCell: (params) => {
        return (
          <Tooltip title={params.value}>
            <span>{params.value}</span>
          </Tooltip>
        );
      },
     },
    // {
    //   field: "parentRoleName",
    //   headerName: "Parent Role",
    //   flex: 1,
    //   minWidth: 120,
    //   renderCell: (params) => {
    //     return (
    //       <Tooltip title={params.value}>
    //         <span>{params.value}</span>
    //       </Tooltip>
    //     );
    //   },
    // },
    {
      field: "description",
      headerName: "Description",
      flex: 1,
      minWidth: 120,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <span>{params.value}</span>
        </Tooltip>
      ),
    },
    // Conditionally include organisationName column
    ...(user?.organisationCategory === "SUPER_ADMIN"
      ? [
          {
            field: "organisationName",
            headerName: "Tenant Name",
            flex: 1,
            minWidth: 120,
            renderCell: (params) => (
              <Tooltip title={params.value}>
                <span>{params.value}</span>
              </Tooltip>
            ),
          },
        ]
      : []),
    {
      field: "createdBy",
      headerName: "Created by",
      flex: 0.6,
      minWidth: 120,
      renderCell: (params) => {
        const shouldShowTooltip = params.value && params.value.length > 20;
        return (
          <Tooltip title={shouldShowTooltip ? params.value : ""}>
            <span>{params.value}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "updatedBy",
      headerName: "Updated by",
      flex: 0.6,
      minWidth: 120,
      renderCell: (params) => {
        const shouldShowTooltip = params.value && params.value.length > 20;
        return (
          <Tooltip title={shouldShowTooltip ? params.value : ""}>
            <span>{params.value}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.4,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
  ];
};

export default Columns;
