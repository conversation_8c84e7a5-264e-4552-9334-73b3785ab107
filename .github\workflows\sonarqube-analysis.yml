name: SonarQube Analysis

on:
  workflow_call:
    secrets:
      SONAR_TOKEN:
        required: true

jobs:
  sonarqube:
    runs-on: [self-hosted, Linux, X64]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Install dependencies
        run: npm ci
      - name: Build
        run: npm run build
      - name: Run Tests (with coverage)
        run: npm run test:coverage
      - name: Install SonarScanner
        run: npm install -g sonarqube-scanner
      - name: Run SonarQube Analysis
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: |
          sonar-scanner \
            -Dsonar.projectKey=donation-receipt-frontend \
            -Dsonar.projectName="Donation Receipt Frontend" \
            -Dsonar.sources=src \
            -Dsonar.tests=src/tests \
            -Dsonar.host.url=http://*************:9000 \
            -Dsonar.login=$SONAR_TOKEN \
            -Dsonar.coverage.exclusions=src/components/ui/**,src/services/configService.ts,src/config/env.ts,src/main.jsx,src/setupTests.js \
            -Dsonar.test.inclusions=src/tests/unit/**/*.test.js,src/tests/unit/**/*.test.jsx,src/tests/unit/**/*.test.ts,src/tests/unit/**/*.test.tsx,src/tests/integration/**/*.test.js,src/tests/integration/**/*.test.jsx,src/tests/integration/**/*.test.ts,src/tests/integration/**/*.test.tsx,src/tests/api/**/*.test.js,src/tests/api/**/*.test.jsx,src/tests/api/**/*.test.ts,src/tests/api/**/*.test.tsx \
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info \
            -Dsonar.sourceEncoding=UTF-8
      - name: Wait for SonarQube Quality Gate
        uses: sonarsource/sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: http://***************:9000