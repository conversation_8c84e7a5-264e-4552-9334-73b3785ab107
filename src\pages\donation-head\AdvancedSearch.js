import { FormControl, Grid, TextField } from "@mui/material";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import { styled } from "@mui/material/styles";
import Tooltip from "@mui/material/Tooltip";
import Typography from "@mui/material/Typography";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import PerfectScrollbar from "react-perfect-scrollbar";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import { AuthContext } from "src/context/AuthContext";

const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const AdvancedSearch = (props) => {
  const {
    open,
    toggle,
    selectedFilters,
    setSearchingState,
    clearAllFilters,
    onApplyFilters,
    tenantsList,
  } = props;

  const {
    setValue,
    control,
    reset,
    handleSubmit,
    formState:{errors}
  } = useForm();

  const [tenantId,setTenantId] = useState("")
  const { user } = useContext(AuthContext)

  useEffect(() => {
    const filterMap = new Map(selectedFilters?.map((filter) => [filter.key, filter.value]));

    setValue("donationHead", filterMap.get("nameFilter") || "");
    setTenantId(filterMap.get("orgIdFilter") || "");
    setValue("description", filterMap.get("descriptionFilter") || "");
  }, [selectedFilters, setValue]);

  const handleCancel = () => {
    reset();
    setTenantId("")
    setSearchingState(false);
    clearAllFilters();
  };

  const handleClose = () => {
    toggle();
  };

  const handleApply = (data) => {
    const filters = [];

    if (tenantId) {
      filters.push({ key: "orgIdFilter", label: "Tenant Name", value: tenantId });
    }
    if (data?.donationHead) {
      filters.push({ key: "nameFilter", label: "Donation Head", value: data.donationHead });
    }
    if (data?.description) {
      filters.push({ key: "descriptionFilter", label: "Description", value: data.description });
    }

    onApplyFilters(filters);
    setSearchingState(true);
    toggle();
  };

  return (
    <>
      <Tooltip title="Advanced Search">
        <CustomAvatar
          variant="rounded"
          sx={{ width: 36, height: 36, cursor: "pointer" }}
          onClick={toggle}
        >
          <Icon icon="tabler:filter" fontSize={27} />
        </CustomAvatar>
      </Tooltip>

      <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={handleClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "85%", sm: 500 } } }}
      >
        <Header
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            position: "relative"
          }}
        >
          <Typography variant="h5" sx={{ ml: 3 }}>
            Advanced Search
          </Typography>
          <Box sx={{ position: "absolute", top: "8px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#d6d6f5",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </Header>

        <PerfectScrollbar options={{ wheelPropagation: false }}>
          <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>
            <Grid container spacing={3} alignItems="center">
              {user?.organisationCategory === "SUPER_ADMIN" && (
                  <Grid item xs={12} sm={12}>
                  <FormControl fullWidth >
                    <Controller
                      name="tenantName"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <SelectAutoComplete
                          id="tenantName"
                          label="Tenant Name"
                          nameArray={tenantsList}
                          value={tenantId}
                          onChange={(event) => {
                            field.onChange(event.target?.value);
                            setTenantId(event.target?.value);
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              )}
          
            <Grid item xs={12} sm={12}>
              <FormControl fullWidth>
                <Controller
                  name="donationHead"
                  control={control}
                  rules={{ required: false }}
                  render={({ field }) => (
                    <NameTextField
                      {...field}
                      size="small"
                      label="Donation Head"
                      InputLabelProps={{ shrink: true }}
                      placeholder="Enter your Donation Head"
                      error={Boolean(errors.donationHead)}
                      helperText={errors.donationHead?.message}
                      aria-describedby="donationHead"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={12}>
              <FormControl fullWidth>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      label="Description"
                      InputLabelProps={{ shrink: true }}
                      placeholder="Enter description"
                      helperText={errors.description?.message}
                      error={Boolean(errors.description)}
                      aria-describedby="description"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            </Grid>
          </Box>
        </PerfectScrollbar>

        <Box
          sx={{
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => theme.spacing(2),
            justifyContent: "flex-end",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Button variant="tonal" sx={{ mr: 3 }} onClick={handleCancel}>
            Clear All
          </Button>
          <Button variant="contained" onClick={handleSubmit(handleApply)} sx={{ mr: 4 }}>
            Apply
          </Button>
        </Box>
      </Drawer>
    </>
  );
};

export default AdvancedSearch;
