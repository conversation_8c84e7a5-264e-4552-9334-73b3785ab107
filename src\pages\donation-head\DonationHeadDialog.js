import { useContext, useEffect, useState } from "react";

// ** Custom Components Imports

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component

import { AuthContext } from "src/context/AuthContext";
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  TextField,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import { useAuth } from "src/hooks/useAuth";

const DonationHeadDialog = ({ open, onClose, formData,fetchUsers, page, pageSize, searchKeyword,tenantsList }) => {
  const theme = useTheme();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    clearErrors,
    control,
    reset,
    formState: { errors },
  } = useForm();
  const { user } = useContext(AuthContext)
  const [saveLoading, setSaveLoading] = useState(false);
  const auth = useAuth();
  const [tenantId, setTenantId] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const handleClose = () => setOpenDialogContent(false);

  const handleSuccess = () => {
    const message = `
        <div> 
          <h3> Donation Head added Successfully.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleSuccessUpdate = () => {
    const message = `
        <div> 
          <h3> Donation Head updated Successfully.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (err) => {
    let message;
    if (err?.response?.status == 400) {
      message = `
        <div>
          <h3>Donation Head already exists!</h3>
        </div>
      `;
    } else {
      message = `
        <div> 
          <h3> Failed to Add Donation Head. Please try again later.</h3>
        </div>
      `;
    }

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailureUpdate = (err) => {
    let message;
    if (err?.response?.status == 400) {
      message = `
        <div>
          <h3>Donation Head already exists!</h3>
        </div>
      `;
    } else {
      message = `
        <div> 
          <h3> Failed to Update Donation Head. Please try again later.</h3>
        </div>
      `;
    }

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  async function submit(data) {
    setSaveLoading(true);
    const fields = {
      name: data.donationHead,
      orgId: user?.organisationCategory === "SUPER_ADMIN" ? tenantId : user?.orgId,
      description: data.description,
    };
    try {
      const response = await auth.postDonationHead(
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailure();
    }
    setSaveLoading(false);
    fetchUsers(page, pageSize, searchKeyword);
    handleCloseDialog();
  }

  async function update(data) {
    setSaveLoading(true);
    const fields = {
        name: data.donationHead,
        orgId: user?.organisationCategory === "SUPER_ADMIN" ? tenantId : user?.orgId,
        description: data.description,
      };
      try {
        const response = await auth.patchDonationHead(
          formData?.id,
          fields,
          handleFailureUpdate,
          handleSuccessUpdate
        );
      } catch (error) {
        console.error("Employee Creation failed:", error);
        handleFailureUpdate();
      }
  
    setSaveLoading(false);
    fetchUsers(page, pageSize, searchKeyword);
    handleCloseDialog();
    
  }

  const handleCloseDialog = () => {
    onClose(); // Close the dialog
  };

  useEffect(() => {
    setValue("description", formData?.description || "");
    setValue("donationHead", formData?.name || "");
    setTenantId(formData?.orgId || "");
  },[formData]);

  return (
    <>
      <Dialog open={open} onClose={handleCloseDialog} maxWidth="xs">
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            height: "50px", // Set fixed height for header
          }}
          textAlign={"center"}
        >
          <Box
            sx={{
              fontSize: {
                xs: 14, // Smaller font size for mobile
                sm: 15, // Slightly larger font size
                md: 17, // Default font size for larger screens
                lg: 16,
              },
              fontWeight: 600, // Bold text if needed
              ml: {
                xs: 3,
                xl: 3,
              },
            }}
          >
            {!formData || Object.keys(formData).length === 0
              ? "Add Donation Head"
              : "Update Donation Head"}
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "10px",
              mr: {
                xs: 5.5,
                sm: 5.5,
                md: 5.5,
                lg: 5.5,
                xl: 5.5,
              },
            }}
          >
            <IconButton
              size="small"
              onClick={handleCloseDialog}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#9a9ae5",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            p: (theme) => `${theme.spacing(6, 8)} !important`,
          }}
        >
          <Grid container spacing={2}>
            {user?.organisationCategory === "SUPER_ADMIN" && (
               <Grid item xs={12} sm={12}>
               <FormControl fullWidth error={Boolean(errors.tenantName)}>
                 <Controller
                   name="tenantName"
                   control={control}
                   rules={{ required: "Tenant Name is required" }}
                   render={({ field }) => (
                     <SelectAutoComplete
                       id="tenantName"
                       label="Tenant Name"
                       nameArray={tenantsList}
                       value={tenantId}
                       onChange={(event) => {
                         field.onChange(event.target?.value);
                         setTenantId(event.target?.value);
                       }}
                     />
                   )}
                 />
                 {errors.tenantName && (
                   <FormHelperText sx={{ color: "error.main" }}>
                     {errors.tenantName.message}
                   </FormHelperText>
                 )}
               </FormControl>
             </Grid>
            )}
            <Grid item xs={12} sm={12}>
              <FormControl fullWidth>
                <Controller
                  name="donationHead"
                  control={control}
                  rules={{ required: "Donation Head is required" }}
                  render={({ field }) => (
                    <NameTextField
                      {...field}
                      size="small"
                      label="Donation Head"
                      InputLabelProps={{ shrink: true }}
                      placeholder="Enter your Donation Head"
                      error={Boolean(errors.donationHead)}
                      helperText={errors.donationHead?.message}
                      aria-describedby="donationHead"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={12}>
              <FormControl fullWidth>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      rows={4}
                      multiline
                      {...field}
                      label="Description"
                      InputLabelProps={{ shrink: true }}
                      helperText={errors.description?.message}
                      error={Boolean(errors.description)}
                      aria-describedby="description"
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5, 4)} !important`,
            height: "50px", // Set fixed height for footer
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={handleCloseDialog}
          >
            Cancel
          </Button>
          {!formData || Object.keys(formData).length === 0 ? (
            <Button
              display="flex"
              justifyContent="center"
              variant="contained"
              color="primary"
              onClick={handleSubmit(submit)}
              sx={{
                mr: {
                  xs: 4,
                  sm: 4,
                  md: 4,
                  lg: 4,
                  xl: 4,
                },
              }}
            >
              {saveLoading ? (
                <CircularProgress color="inherit" size={22} />
              ) : (
                "Save"
              )}
            </Button>
          ) : (
            <Button
              display="flex"
              justifyContent="center"
              variant="contained"
              color="primary"
              onClick={handleSubmit(update)}
              sx={{
                mr: {
                  xs: 4,
                  sm: 4,
                  md: 4,
                  lg: 4,
                  xl: 4,
                },
              }}
            >
              {saveLoading ? (
                <CircularProgress color="inherit" size={22} />
              ) : (
                "Update"
              )}
            </Button>
          )}
        </DialogActions>
      </Dialog>
      <Dialog
        open={openDialogContent}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default DonationHeadDialog;
