sonar.projectKey=donation-receipt-frontend
sonar.projectName=Donation Receipt Frontend
sonar.host.url=http://localhost:9000
# sonar.token should be set via environment variable SONAR_TOKEN
# or passed as a command line parameter with -Dsonar.login=
# sonar.token=squ_c224e0b0ce4c32c9b94b912f5418bece9a4d0fc0
sonar.sources=src
sonar.tests=src/tests
sonar.test.inclusions=src/tests/unit/**/*.test.js,src/tests/unit/**/*.test.jsx,src/tests/unit/**/*.test.ts,src/tests/unit/**/*.test.tsx,src/tests/integration/**/*.test.js,src/tests/integration/**/*.test.jsx,src/tests/integration/**/*.test.ts,src/tests/integration/**/*.test.tsx,src/tests/api/**/*.test.js,src/tests/api/**/*.test.jsx,src/tests/api/**/*.test.ts,src/tests/api/**/*.test.tsx
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.sourceEncoding=UTF-8
sonar.coverage.exclusions=src/components/ui/**,src/services/configService.ts,src/config/env.ts,src/main.jsx,src/setupTests.js
