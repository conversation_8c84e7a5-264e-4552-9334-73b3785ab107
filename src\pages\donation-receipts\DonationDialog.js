// src/components/DonationDialog.js

import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  TextField,
  Typography,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import { useContext, useEffect, useState } from "react";
import axios from "axios";
import { getAuthorizationHeaders } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import { AuthContext } from "src/context/AuthContext";

const DonationDialog = ({
  open,
  onClose,
  formData,
  control,
  errors,
  setValue,
  handleSubmit,
  donationTypesData,
  paymentModeData,
  paymentTypeData,
  tenantsList,
  donorsList,
  fetchReceipts,
  page,
  pageSize,
  searchKeyword,
  donationHeadsList,
  tenantId,
  setTenantId,
  donorId,
  setDonorId,
  donorData,
}) => {
  const auth = useAuth();
  const [donationType, setDonationType] = useState(null);
  const { user, listValues } = useContext(AuthContext);
  const [payment, setPayment] = useState(null);
  const [paymentType, setPaymentType] = useState(null);
  const panRegex = /^[A-Z]{3}[PCHFATBLJG][A-Z][0-9]{4}[A-Z]$/;
  const validFourthChars = ["P", "C", "H", "F", "A", "T", "B", "L", "J", "G"];

  // Place this outside the component or in a utility file
  const d = [
    [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    [1, 2, 3, 4, 0, 6, 7, 8, 9, 5],
    [2, 3, 4, 0, 1, 7, 8, 9, 5, 6],
    [3, 4, 0, 1, 2, 8, 9, 5, 6, 7],
    [4, 0, 1, 2, 3, 9, 5, 6, 7, 8],
    [5, 9, 8, 7, 6, 0, 4, 3, 2, 1],
    [6, 5, 9, 8, 7, 1, 0, 4, 3, 2],
    [7, 6, 5, 9, 8, 2, 1, 0, 4, 3],
    [8, 7, 6, 5, 9, 3, 2, 1, 0, 4],
    [9, 8, 7, 6, 5, 4, 3, 2, 1, 0],
  ];

  const p = [
    [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    [1, 5, 7, 6, 2, 8, 3, 0, 9, 4],
    [5, 8, 0, 3, 7, 9, 6, 1, 4, 2],
    [8, 9, 1, 6, 0, 4, 3, 5, 2, 7],
    [9, 4, 5, 3, 1, 2, 6, 8, 7, 0],
    [4, 2, 8, 6, 5, 7, 3, 9, 0, 1],
    [2, 7, 9, 3, 8, 0, 6, 4, 1, 5],
    [7, 0, 4, 6, 9, 1, 3, 2, 5, 8],
  ];

  const inv = [0, 4, 3, 2, 1, 5, 6, 7, 8, 9];

  function verhoeffValidate(num) {
    let c = 0;
    const myArray = num.split("").reverse().map(Number);
    for (let i = 0; i < myArray.length; i++) {
      c = d[c][p[i % 8][myArray[i]]];
    }
    return c === 0;
  }

  useEffect(() => {
    setTenantId(formData?.orgId);
    setDonorId(formData?.tenantDonorsDTO?.id || formData?.selfRegisteredDonorDTO?.id);
    setPayment(formData?.metaData?.paymentMode);
    setPaymentType(formData?.metaData?.paymentType);
    setValue("amount", formData?.metaData?.amount);
    setValue("paymentDetails", formData?.metaData?.paymentDetails);
    setValue("reference", formData?.metaData?.reference);
    setValue("additionalNotes", formData?.metaData?.additionalNotes);
    setValue("donationDate", formData?.receiptDate);
    setDonation(formData?.donationHeadId);
    setDonationType(formData?.donationTypeId);
  }, [formData]);

  const [donation, setDonation] = useState(null);
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const handleClose = () => setOpenDialogContent(false);

  const handleDialogClose = () => {
    setDonationType("")
    setPayment("")
    setPaymentType("")
    onClose()
  }

  const handleDonationChange = (newValue) => {
    setDonation(newValue);
  };

  const handleSuccess = () => {
    const message = `
        <div> 
          <h3> Receipt added Successfully.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (err) => {
    let message;
    if (err?.response?.status == 400) {
      message = `
        <div>
          <h3>Receipt already exists!</h3>
        </div>
      `;
    } else {
      message = `
        <div> 
          <h3> Failed to Add Receipt. Please try again later.</h3>
        </div>
      `;
    }

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleSuccessUpdate = () => {
    const message = `
        <div> 
          <h3> Receipt updated Successfully.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailureUpdate = (err) => {
    let message;
    if (err?.response?.status == 400) {
      message = `
        <div>
          <h3>Receipt already exists!</h3>
        </div>
      `;
    } else {
      message = `
        <div> 
          <h3> Failed to update Receipt. Please try again later.</h3>
        </div>
      `;
    }

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  async function submit(data) {
    const fields = {
      orgId:
        user?.organisationCategory === "SUPER_ADMIN" ? tenantId : user?.orgId,
      donorId:donorId,
      metaData: {
        amount: data?.amount,
        paymentMode: payment,
        paymentType:paymentType,
        paymentDetails: data?.paymentDetails,
        reference: data?.reference,
        additionalNotes: data?.additionalNotes,
      },
      donationTypeId: donationType,
      donationHeadId: donation,
      receiptDate: data?.donationDate,
    };
    try {
      const response = await auth.postDonationReceipt(
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailure();
    }
    fetchReceipts(page, pageSize, searchKeyword);
    handleDialogClose();
  }

  async function update(data) {
    const fields = {
      orgId:
        user?.organisationCategory === "SUPER_ADMIN" ? tenantId : user?.orgId,
      donorId:donorId,
      metaData: {
        amount: data?.amount,
        paymentType:paymentType,
        paymentMode: payment,
        paymentDetails: data?.paymentDetails,
        reference: data?.reference,
        additionalNotes: data?.additionalNotes,
      },
      donationTypeId: donationType,
      donationHeadId: donation,
      receiptDate: data?.donationDate,
    };
    try {
      const response = await auth.patchDonationReceipt(
        formData?.id,
        fields,
        handleFailureUpdate,
        handleSuccessUpdate
      );
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailureUpdate();
    }
    fetchReceipts(page, pageSize, searchKeyword);
    handleDialogClose();
  }

  const handlePaymentChange = (newValue) => {
    setPayment(newValue);
  };

  return (
    <>
      <Dialog open={open} onClose={handleDialogClose} maxWidth="md">
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "flex-start", md: "flex-start" },
            fontSize: { xs: 20, md: 26 },
            height: "45px",
          }}
        >
          {!formData || Object.keys(formData).length === 0
            ? "Add New Receipt"
            : "Update New Receipt"}
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "10px",
            }}
          >
            <IconButton
              size="small"
              onClick={handleDialogClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#d6d6f5",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          <>
            <Grid
              sx={{
                backgroundColor: "#d6d6f5",
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
                mb: 4,
                mt: -3,
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Donor Information
              </Typography>
            </Grid>
            <Grid container spacing={2}>
              {user?.organisationCategory === "SUPER_ADMIN" && (
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth error={Boolean(errors.tenantName)}>
                    <Controller
                      name="tenantName"
                      control={control}
                      rules={{
                        required:
                          !formData || Object.keys(formData).length === 0
                            ? "Tenant Name is required"
                            : false,
                      }}
                      render={({ field }) => (
                        <SelectAutoComplete
                          id="tenantName"
                          label="Tenant Name"
                          nameArray={tenantsList}
                          value={tenantId}
                          onChange={(event) => {
                            field.onChange(event.target?.value);
                            setTenantId(event.target?.value);
                          }}
                        />
                      )}
                    />
                    {errors.tenantName && (
                      <FormHelperText sx={{ color: "error.main" }}>
                        {errors.tenantName.message}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              )}
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth error={Boolean(errors.tenantName)}>
                  <Controller
                    name="donorName"
                    control={control}
                    rules={{
                      required:
                        !formData || Object.keys(formData).length === 0
                          ? "Donor is required"
                          : false,
                    }}
                    render={({ field }) => (
                      <SelectAutoComplete
                        id="donorName"
                        label="Select Donor"
                        nameArray={donorsList}
                        value={donorId}
                        onChange={(event) => {
                          field.onChange(event.target?.value);
                          setDonorId(event.target?.value);
                        }}
                      />
                    )}
                  />
                  {errors.donorName && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.donorName.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid container item xs={12} md={4} spacing={2} sx={{mt:0}}>
                <Grid item>
                  <Typography className="data-field">
                    {" "}
                    Mobile Number :
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography style={{ fontWeight: "bold" }}>
                    {donorData?.contactNumber}
                  </Typography>
                </Grid>
              </Grid>
                <Grid container item xs={12} md={4} spacing={2} sx={{ml:0}}  >
                <Grid item>
                  <Typography className="data-field">
                    {" "}
                   Email :
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography style={{ fontWeight: "bold" }}>
                    {donorData?.email}
                  </Typography>
                </Grid>
              </Grid>

               <Grid container item xs={12} md={4} spacing={2} >
                <Grid item>
                  <Typography className="data-field">
                    {" "}
                   Pan number :
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography style={{ fontWeight: "bold" }}>
                    {donorData?.panNo}
                  </Typography>
                </Grid>
              </Grid>        

                <Grid container item xs={12} md={12} spacing={2} sx={{ml:0}} >
                <Grid item>
                  <Typography className="data-field">
                    {" "}
                   Address :
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography style={{ fontWeight: "bold" }}>
                    {donorData?.donorMetaData?.address}
                  </Typography>
                </Grid>
              </Grid>                    
            </Grid>
          </>

          <>
            <Grid
              sx={{
                backgroundColor: "#d6d6f5",
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
                mb: 5,
                mt: 2,
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Donor Details
              </Typography>
            </Grid>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth error={Boolean(errors.donationType)}>
                  <Controller
                    name="donationType"
                    control={control}
                    rules={{
                      required:
                        !formData || Object.keys(formData).length === 0
                          ? "Donation Type is required"
                          : donationType
                          ? false
                          : "Donation Type is required",
                    }}
                    render={({ field }) => (
                      <SelectAutoComplete
                        id="donationType"
                        label="Donation Type"
                        nameArray={donationTypesData}
                        value={donationType}
                        onChange={(event) => {
                          field.onChange(event.target?.value);
                          setDonationType(event.target.value);
                        }}
                      />
                    )}
                  />
                  {errors.donationType && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.donationType.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth error={Boolean(errors.donationHead)}>
                  <Controller
                    name="donationHead"
                    control={control}
                    rules={{
                      required:
                        !formData || Object.keys(formData).length === 0
                          ? "Donation Head is required"
                          : donation
                          ? false
                          : "Donation Head is required",
                    }}
                    render={({ field }) => (
                      <SelectAutoComplete
                        id="donationHead"
                        label="Donation Head"
                        nameArray={donationHeadsList}
                        value={donation}
                        onChange={(event) => {
                          field.onChange(event.target?.value);
                          handleDonationChange(event.target.value);
                        }}
                      />
                    )}
                  />
                  {errors.donationHead && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.donationHead.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="amount"
                    control={control}
                    rules={{ required: "Amount is required" }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Amount"
                        placeholder="Enter your Amount"
                        error={Boolean(errors.amount)}
                        helperText={errors.amount?.message}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="donationDate"
                    control={control}
                    rules={{ required: "Donation Date is required" }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        type="date"
                        InputLabelProps={{ shrink: true }}
                        label="Donation Date"
                        placeholder="select Donation Date"
                        error={Boolean(errors.donationDate)}
                        helperText={errors.donationDate?.message}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={4}>
                <FormControl fullWidth error={Boolean(errors.paymentMode)}>
                  <Controller
                    name="paymentType"
                    control={control}
                    rules={{
                      required:
                        !formData || Object.keys(formData).length === 0
                          ? "Payment Type is required"
                          : paymentType
                          ? false
                          : "Payment Type is required",
                    }}
                    render={({ field }) => (
                      <SelectAutoComplete
                        id="paymentMode"
                        label="Payment Type"
                        nameArray={paymentTypeData}
                        value={paymentType}
                        onChange={(event) => {
                          field.onChange(event.target?.value);
                          setPaymentType(event.target.value);
                        }}
                      />
                    )}
                  />
                  {errors.paymentMode && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.paymentMode.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {listValues?.find((item) => item.id === paymentType)?.name ===
                "Offline" && (
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth error={Boolean(errors.paymentMode)}>
                    <Controller
                      name="paymentMode"
                      control={control}
                      rules={{
                        required:
                          !formData || Object.keys(formData).length === 0
                            ? "Payment Mode is required"
                            : payment
                            ? false
                            : "Payment is required",
                      }}
                      render={({ field }) => (
                        <SelectAutoComplete
                          id="paymentMode"
                          label="Payment Mode"
                          nameArray={paymentModeData}
                          value={payment}
                          onChange={(event) => {
                            field.onChange(event.target?.value);
                            handlePaymentChange(event.target.value);
                          }}
                        />
                      )}
                    />
                    {errors.paymentMode && (
                      <FormHelperText sx={{ color: "error.main" }}>
                        {errors.paymentMode.message}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              )}

              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="paymentDetails"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        InputLabelProps={{ shrink: true }}
                        label="Payment Details"
                        placeholder="Enter Payment Details"
                        error={Boolean(errors.paymentDetails)}
                        helperText={errors.paymentDetails?.message}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </>
          {listValues?.find((item) => item.id === paymentType)?.name ===
            "Online" && (
            <Grid
              sx={{
                // backgroundColor: "#d6d6f5",
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
                mb: 5,
                mt: 2,
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                *Note : You'll get an send payment link option in actions after
                submitting this receipt
              </Typography>
            </Grid>
          )}

          <>
            <Grid
              sx={{
                backgroundColor: "#d6d6f5",
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
                mb: 5,
                mt: 2,
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Additional Information
              </Typography>
            </Grid>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <Controller
                    name="reference"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Reference"
                        placeholder="Enter Reference"
                        error={Boolean(errors.reference)}
                        helperText={errors.reference?.message}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={12}>
                <FormControl fullWidth>
                  <Controller
                    name="additionalNotes"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        multiline
                        rows={3}
                        InputLabelProps={{ shrink: true }}
                        label="Additional Notes "
                        placeholder="Enter Additional Notes "
                        error={Boolean(errors.reference)}
                        helperText={errors.reference?.message}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </>
        </DialogContent>

        <DialogActions
          sx={{
            justifyContent: "flex-end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            height: "50px",
          }}
        >
          <Button variant="outlined" color="primary" onClick={handleDialogClose}>
            Cancel
          </Button>
          {!formData || Object.keys(formData).length === 0 ? (
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmit(submit)}
            >
              Save Receipt
            </Button>
          ) : (
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmit(update)}
            >
              Update Receipt
            </Button>
          )}
        </DialogActions>
      </Dialog>
      <Dialog
        open={openDialogContent}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default DonationDialog;
