import { useContext, useEffect, useRef, useState } from "react";

// ** MUI Imports
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import TextField from "@mui/material/TextField";
import Tooltip from "@mui/material/Tooltip";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
// ** Third Party Imports
import { Autocomplete, Divider } from "@mui/material";
import axios from "axios";
import { Controller, useForm } from "react-hook-form";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import SocietyMembers from "./SocietyMembers";
import { AuthContext } from "src/context/AuthContext";
import CustomChip from "src/@core/components/mui/chip";
const userStatusObj = {
  VERIFIED: "Verified",
  PENDING: "Pending",
};
const MembersList = ({ formData, onUpdate,setExistingList,setNewList,newList,tenantRowData }) => {
  const {
    control,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: formData?.members?.orgIndividualsList || [], // Initialize form fields with existing data
  });

  const { user } = useContext(AuthContext)

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [societyMembers, setSocietyMembers] = useState(
    formData?.members?.orgIndividualsList || []
  );

  const [currentRow, setCurrentRow] = useState(null);
  const [societyMemberOpen, setSocietyMemberOpen] = useState(false);

  // Watch all fields for changes
  const watchedFields = watch();

  // Update formData on any change
  const previousWatchedFields = useRef();

  useEffect(() => {
    // Compare previous watched fields with current watched fields
    const hasWatchedFieldsChanged =
      JSON.stringify(previousWatchedFields.current) !==
      JSON.stringify(watchedFields);
    if (
      hasWatchedFieldsChanged ||
      societyMembers !== previousWatchedFields.current?.societyMembers
    ) {
      onUpdate({
        ...watchedFields,
        orgIndividualsList: societyMembers, // Add extra field
      });
      previousWatchedFields.current = watchedFields; // Update the ref with the latest value
    }
  }, [watchedFields, societyMembers, onUpdate]);

  const handleSocietyMemberDialogOpen = () => {
    setCurrentRow(null);
    setSocietyMemberOpen(true);
  };
  const handleSocietyMemberDialogClose = () => {
    setSocietyMemberOpen(false);
    setCurrentRow(null);
  };

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };
  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };


    const [roles, setRoles] = useState([]);
    const rolesArray = roles?.map((item) => ({
      value: item?.id,
      key: item?.roleName,
      description: item?.roleDescription,
    }));
  
    useEffect(() => {

      const id=user?.organisationCategory === "SUPER_ADMIN"? tenantRowData?.orgId: user?.orgId;

      axios({
        method: "get",
        url:
          getUrl(authConfig.rolesDropdownEndpoint) +
          "/get-roles-by-orgId/" +
          id,
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          setRoles(res.data);
          
        })
        .catch((err) => console.log("error", err));
    }, [currentRow,user]);
 
  const cols = [
    {
      field: "name",
      headerName: "Name",
      minWidth: 105,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.name}>
          <span>{params.row.name}</span>
        </Tooltip>
      ),
    },
    {
      field: "isVerified",
      headerName: "Verification Status",
      minWidth: 95,
      flex: 1,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isVerified)}
            color={row.isVerified === "VERIFIED" ? "success" : "warning"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "mobileNumber",
      headerName: "Contact No.",
      minWidth: 105,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.mobileNumber}>
          <a
            href={`tel:${params.row.mobileNumber}`}
            style={{ textDecoration: "none", color: "blue" }}
          >
            {params.row.mobileNumber}
          </a>
        </Tooltip>
      ),
    },
    {
      field: "email",
      headerName: "Email",
      minWidth: 110,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.email}>
          <a
            href={`mailto:${params.row.email}`}
            style={{ textDecoration: "none", color: "blue" }}
          >
            {params.row.email}
          </a>
        </Tooltip>
      ),
    },
    {
      field: "roleId",
      headerName: "Role",
      minWidth: 95,
      flex: 1,
        renderCell: (params) => {
        const org = rolesArray?.find(
          (item) => item?.value === params?.row?.roleId
        );
        return (
          <Tooltip title={org ? org?.key : ""}>
            <span>{org ? org?.key : ""}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 80,
      flex: 1,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
        };

        const onClickViewProfile = () => {
          setSocietyMemberOpen(true);
          handleCloseMenuItems();
        };
        const onClickDeleteProfile = () => {
          const sm = societyMembers.filter(
            (member) => member?.id !== currentRow?.id
          );
          setSocietyMembers(sm);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={onClick}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              <MenuItem onClick={onClickDeleteProfile}>Delete</MenuItem>
            </Menu>
          </div>
        );
      },
    },
  ];

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "right",
          mr: { xs: 5, sm: 6, md: 8, xl: 23, lg: 30 },
        }}
      >
        <div>
          <Button
            sx={{ margin: "10px" }}
            variant="contained"
            onClick={handleSocietyMemberDialogOpen}
          >
            Add member
          </Button>
        </div>
      </Box>
      <Grid item xs={12} sm={4}>
        <>
          <SocietyMembers
            open={societyMemberOpen}
            onClose={handleSocietyMemberDialogClose}
            societyMembers={societyMembers}
            setSocietyMembers={setSocietyMembers}
            rowData={currentRow}
            setExistingList={setExistingList}
            setNewList={setNewList}
            newList={newList}
            rolesArray={rolesArray}
          />
        </>
      </Grid>
      {/* {societyMembers?.length > 0 && ( */}
      <div style={{ height: "50%", width: "100%" }}>
        <DataGrid
          rows={societyMembers || []}
          columns={cols}
          autoHeight
          checkboxSelection
          pagination
          pageSize={pageSize}
          page={page - 1}
          rowsPerPageOptions={rowsPerPageOptions}
          rowCount={rowCount}
          paginationMode="server"
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          rowHeight={38}
          headerHeight={38}
        />
      </div>
      {/* )} */}
    </>
  );
};

export default MembersList;
