import { Tooltip } from "@mui/material";
import CustomChip from "src/@core/components/mui/chip";
import { useContext } from "react";
import { AuthContext } from "src/context/AuthContext";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const Columns = () => {
  const { user } = useContext(AuthContext);
  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  return [
    {
      field: "name",
      headerName: "Name",
      flex: 0.8,
      minWidth: 100,
      renderCell: (params) => (
        <Tooltip title={params.row.name}>
          <span>{params.row.name}</span>
        </Tooltip>
      ),
    },
    ...(user?.organisationCategory === "SUPER_ADMIN"
      ? [
        {
          field: "companyName",
          headerName: "Tenant Name",
          flex: 1,
          minWidth: 120,
          renderCell: (params) => {
            const shouldShowTooltip = params.value && params.value.length > 25;
            return (
              <Tooltip title={shouldShowTooltip ? params.value : ""}>
                <span>{params.value}</span>
              </Tooltip>
            );
          },
        }
        ]
      : []),
    {
      field: "email",
      headerName: "Email",
      flex: 1.11,
      minWidth: 125,
      renderCell: (params) => {
        return (
          <Tooltip title={params.value}>
            <a href={`mailto:${params.value}`} style={{ color: "#6666ff" }}>
              {params.value}
            </a>
          </Tooltip>
        );
      },
    },
    {
      field: "mobileNumber",
      headerName: "Mobile No.",
      flex: 0.6,
      minWidth: 80,
      renderCell: (params) => {
        return (
          <Tooltip title={params.value}>
            <a href={`tel:${params.value}`} style={{ color: "#6666ff" }}>
              {params.value}
            </a>
          </Tooltip>
        );
      },
    },    
    {
      field: "roleName",
      headerName: "Role",
      flex: 1,
      minWidth: 120,
      renderCell: (params) => {
        const shouldShowTooltip = params.value && params.value.length > 10;
        return (
          <Tooltip title={shouldShowTooltip ? params.value : ""}>
            <span>{params.value}</span>
          </Tooltip>
        );
      },
    },
   
    {
      field: "createdBy",
      headerName: "Created by",
      flex: 0.6,
      minWidth: 120,
      renderCell: (params) => {
        const shouldShowTooltip = params.value && params.value.length > 20;
        return (
          <Tooltip title={shouldShowTooltip ? params.value : ""}>
            <span>{params.value}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "updatedBy",
      headerName: "Updated by",
      flex: 0.6,
      minWidth: 120,
      renderCell: (params) => {
        const shouldShowTooltip = params.value && params.value.length > 20;
        return (
          <Tooltip title={shouldShowTooltip ? params.value : ""}>
            <span>{params.value}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.4,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
  ];
};

export default Columns;
