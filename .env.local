# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8080/donationreceipt/


# JWT Configuration
NEXT_PUBLIC_JWT_EXPIRATION= 5m
NEXT_PUBLIC_JWT_SECRET=verylongchidhganilocalsecretyouwillnotunderstanddahsdsjalkdjsadnsadsalkdjsalkdhacydsahfdlkfjdslkfjdsfpodsifpdsycxiovlkcxvfdfjdshfiusyfuyrerewjrewfdsadsadsadsadsadasdsadsadsadcxvcxvdf
NEXT_PUBLIC_JWT_REFRESH_TOKEN_SECRET= 7c4c1c50-3230-45bf-9eae-c9b2e401c767

# OAuth2 Configuration
NEXT_PUBLIC_OAUTH2_REDIRECT_URI=http://localhost:3000/houzer/oauth2/redirect
NEXT_PUBLIC_GOOGLE_AUTH_URL=http://localhost:8080/houzer/oauth2/authorize/google?redirect_uri=http://localhost:3000/houzer/oauth2/redirect
NEXT_PUBLIC_FACEBOOK_AUTH_URL=http://localhost:8080/houzer/oauth2/authorize/facebook?redirect_uri=http://localhost:3000/houzer/oauth2/redirect

# Guest Application URL
GUEST_APPLICATION=https://donation-reciept.houzer.co.in

# Access Token
NEXT_PUBLIC_ACCESS_TOKEN=accessToken

# Razorpay client Id
RZP_CLIENT_ID=rzp_test_zKgAOeC4Kqzl31
