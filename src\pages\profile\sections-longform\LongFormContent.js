import ArchitectureIcon from "@mui/icons-material/Architecture";
import AssignmentIcon from "@mui/icons-material/Assignment";
import ContactMailIcon from "@mui/icons-material/ContactMail";
import HomeIcon from "@mui/icons-material/Home";
import InfoIcon from "@mui/icons-material/Info";
import MapIcon from "@mui/icons-material/Map";
import PreviewIcon from "@mui/icons-material/Preview";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import axios from "axios";
import { useCallback, useContext, useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import dynamic from "next/dynamic";
import { extractNonNullFields } from "src/@core/components/custom-components/ExtractNonNullFields";


const ProfileDetails = dynamic(() =>
  import("./ProfileDetails")
);
const OrganisationDetails = dynamic(() =>
  import("./OrganisationDetails")
);
// Lazy load components
const Documents = dynamic(() =>
  import("./Documents")
);
const MembersList = dynamic(() => import("./MembersList"));
const PreviewSection = dynamic(() => import("./LongFormPreview"));

const LongFormContent = ({ employeesData, initialFormData }) => {
  const { user, patchCHSProfile } = useContext(AuthContext);
  const [currentStep, setCurrentStep] = useState(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [dialogMessage, setDialogMessage] = useState("");
  const [dialogSuccess, setDialogSuccess] = useState(false);
  const [formDataOne, setFormData] = useState(initialFormData());
  const [newList,setNewList] = useState([])
  const [existingList,setExistingList] = useState([])
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [selectedFiles2,setSelectedFiles2] = useState([])
  const [selectedFiles3,setSelectedFiles3] = useState([])

  const sections = [
    {
      id: "profileDetails",
      title: "Basic Profile",
      component: ProfileDetails,
      icon: <HomeIcon />,
    },
    {
      id: "organisationDetails",
      title: "Trust Information",
      component: OrganisationDetails,
      icon: <MapIcon />,
    },
    {
      id: "documents",
      title: "Documents",
      component: Documents,
      icon: <ContactMailIcon />,
    },
    {
      id: "members",
      title: "Members",
      component: MembersList,
      icon: <ArchitectureIcon />,
    },
    {
      id: "preview",
      title: "Preview",
      component: PreviewSection,
      icon: <PreviewIcon />,
    },
  ];

  // Handle updates for individual sections
  const handleFieldUpdate = useCallback((sectionId, updatedData) => {
    setFormData((prevData) => ({ ...prevData, [sectionId]: updatedData }));
  }, []);

  const handleStepChange = (index) => {
    setCurrentStep(index);
  };

  const handleNext = () => {
    if (currentStep < sections?.length - 1) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const [profileData, setProfileData] = useState({});
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  };
  const fetchOrganisationData = () =>{
    const url =
    getUrl(authConfig.organisationsEndpoint) +
    "/org-basic-profile/" +
    user?.orgId;

  axios({
    method: "get",
    url: url,
    headers: getAuthorizationHeaders(),
  })
    .then((res) => {
      setProfileData(res.data);
      const profileDTO = res.data;
      setFormData({
        profileDetails: {
          name: profileDTO?.name || "",
          email: profileDTO?.email || "",
          mobileNumber: profileDTO?.mobileNumber || "",
        },
        organisationDetails: {
          trustName: profileDTO?.trustName || "",
          orgEmail:profileDTO?.orgEmail || "",
          registrationNo: profileDTO?.registrationNo || "",
          panNo: profileDTO?.panNo || "",
          g80RegistrationNo:profileDTO?.g80RegistrationNo || "",
          state: profileDTO?.state || "",
          address: profileDTO?.address || "",
          pinCode: profileDTO?.pinCode || "",
        },
        documents:{
          logoFileLocation:profileDTO?.logoFileLocation || "",
          g80CertificationFileLocationPageOne:profileDTO?.g80CertificationFileLocationPageOne || "",
          g80CertificationFileLocationPageTwo:profileDTO?.g80CertificationFileLocationPageTwo || ""
        },
        members: {
          orgIndividualsList:profileDTO?.orgIndividualsList || [],
        },
      });
    })
    .catch((err) => console.log("CHS Data error", err));
  }
  async function handleSaveProgress() {
    const mappings = {
      name: formDataOne?.profileDetails?.name,
      mobileNumber: formDataOne?.profileDetails?.mobileNumber,
      email: formDataOne?.profileDetails?.email,
      trustName: formDataOne?.organisationDetails?.trustName,
      orgEmail: formDataOne?.organisationDetails?.orgEmail,
      address: formDataOne?.organisationDetails?.address,
      registrationNo: formDataOne?.organisationDetails?.registrationNo,
      panNo: formDataOne?.organisationDetails?.panNo,
      g80RegistrationNo: formDataOne?.organisationDetails?.g80RegistrationNo,
      state: formDataOne?.organisationDetails?.state,
      pinCode: formDataOne?.organisationDetails?.pinCode,
      addUnVerifiedIndividualsList:newList,
      editOrgIndividuals:existingList
    };

    const formData = new FormData();

    
    formData.append(
      "patchBasicProfileDTO",
      JSON.stringify(mappings)
    );
    formData.append("file", selectedFiles[0]);
    formData.append("file80GOne",selectedFiles2[0]);
    formData.append("file80GTwo",selectedFiles3[0]);

    let orgId = user?.orgId;

    await patchCHSProfile(
      orgId,
      formData,
      () => {
        const message = ` Profile Updated Successfully`;
        setDialogMessage(message);
        setDialogSuccess(true);
      },
      () => {
        const message = `Failed to update  Profile`;
        setDialogMessage(message);
        setDialogSuccess(true);
      }
    );

    setCurrentStep(0);
    fetchOrganisationData();
    setSelectedFiles([]);
    setSelectedFiles2([]);
    setSelectedFiles3([]);

  }

  async function handleSubmit() {

    const mappings = {
      name: formDataOne?.profileDetails?.name,
      mobileNumber: formDataOne?.profileDetails?.mobileNumber,
      email: formDataOne?.profileDetails?.email,
      trustName: formDataOne?.organisationDetails?.trustName,
      orgEmail: formDataOne?.organisationDetails?.orgEmail,
      address: formDataOne?.organisationDetails?.address,
      registrationNo: formDataOne?.organisationDetails?.registrationNo,
      panNo: formDataOne?.organisationDetails?.panNo,
      g80RegistrationNo: formDataOne?.organisationDetails?.g80RegistrationNo,
      state: formDataOne?.organisationDetails?.state,
      pinCode: formDataOne?.organisationDetails?.pinCode,
      addUnVerifiedIndividualsList:newList,
      editOrgIndividuals:existingList
    };

    const formData = new FormData();

    
    formData.append(
      "patchBasicProfileDTO",
      JSON.stringify(mappings)
    );
    formData.append("file", selectedFiles[0]);
    formData.append("file80GOne",selectedFiles2[0]);
    formData.append("file80GTwo",selectedFiles3[0]);
  
    
    let orgId = user?.orgId;

    await patchCHSProfile(
      orgId,
      formData,
      () => {
        const message = `Profile Updated Successfully`;
        setDialogMessage(message);
        setDialogSuccess(true);
      },
      () => {
        const message = `Failed to update Profile`;
        setDialogMessage(message);
        setDialogSuccess(true);
      }
    );
    setCurrentStep(0);
    fetchOrganisationData();
    setSelectedFiles([]);
    setSelectedFiles2([]);
    setSelectedFiles3([]);
  }

  useEffect(() => {
    fetchOrganisationData();
  }, []);

  const renderSection = () => {
    const ActiveComponent = sections[currentStep]?.component;
    if (!ActiveComponent) return null;
    const sectionId = sections[currentStep].id;

    if (Object.keys(profileData)?.length === 0) {
      return null; // or a loading indicator like <Loader />
    }
    return (
      <ActiveComponent
        formData={formDataOne} // Pass the specific section's data
        onUpdate={(updatedData) => handleFieldUpdate(sectionId, updatedData)} // Update the specific section's data
        employeesData={employeesData}
        setExistingList={setExistingList}
        setNewList={setNewList}
        newList={newList}
        selectedFiles={selectedFiles}
        setSelectedFiles={setSelectedFiles}
        selectedFiles2={selectedFiles2}
        setSelectedFiles2={setSelectedFiles2}
        selectedFiles3={selectedFiles3}
        setSelectedFiles3={setSelectedFiles3}
      />
    );
  };

  const handleCloseDialog = () => {
    setDialogSuccess(false);
  };

  return (
    <>
      {/* Parent Container */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          height: {
            sm: 440, // Height for small screens (tablets)
            md: 480, // Height for medium screens (desktops)
            lg: 600, // Height for large screens
            xl: 600, // Height for extra large screens
          },
          position: "relative",
          overflow: "hidden",
          mt: 2,
        }}
      >
        {/* Content Area */}
        <Box
          sx={{ display: "flex", flex: 1, width: "90vw", overflow: "hidden" }}
        >
          <Box
            sx={{
              borderRight: isMobile ? "none" : 1,
              borderColor: "divider",
              overflowY: "auto",
            }}
          >
            <List
              sx={{
                display: "flex",
                flexDirection: isMobile ? "row" : "column", // Horizontal list in mobile
                overflowX: isMobile ? "auto" : "hidden", // Horizontal scroll for mobile
                p: isMobile ? 1 : 0,
                gap: isMobile ? 1 : 0,
              }}
            >
              {sections?.map((section, index) => (
                <ListItem
                  button
                  key={section.id}
                  selected={currentStep === index}
                  onClick={() => handleStepChange(index)}
                  sx={{
                    backgroundColor:
                      currentStep === index ? "#d6d6f5" : "inherit",
                    color: currentStep === index ? "#ffffff" : "inherit",
                    "&:hover": {
                      backgroundColor:
                        currentStep === index
                          ? "#d6d6f5"
                          : "rgba(0, 0, 0, 0.04)",
                      color: currentStep === index ? "#ffffff" : "inherit",
                    },
                  }}
                >
                  <ListItemIcon
                    sx={{
                      color: currentStep === index ? "#FFFFFF" : "#757575",
                      backgroundColor:
                        currentStep === index ? "#1f1f7a" : "#F5F5F5",
                      borderRadius: "4px",
                      width: "30px",
                      height: "30px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      marginRight: 2,
                      transition:
                        "background-color 0.3s ease, color 0.3s ease, transform 0.3s ease",
                      transform:
                        currentStep === index ? "scale(1.1)" : "scale(1)",
                    }}
                  >
                    {section.icon}
                  </ListItemIcon>

                  <ListItemText
                    primary={
                      <Typography
                        variant="body1"
                        fontWeight={currentStep === index ? "bold" : "normal"}
                      >
                        {section.title}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
            {isMobile && <Box sx={{ flex: 1 }}>{renderSection()}</Box>}
          </Box>
          {!isMobile && (
            <Box
              sx={{
                flex: 1,
                display: "flex",
                flexDirection: "column",
                p: 3,
                overflow: "auto",
              }}
            >
              {/* Section Content */}
              <Box sx={{ flex: 1 }}>{renderSection()}</Box>
            </Box>
          )}
        </Box>

        {/* Divider and DialogActions */}
        <Box
          sx={{
            borderTop: 1,
            borderColor: "divider",
            display: "flex",
            justifyContent: currentStep === 0 ? "flex-end" : "space-between",
            alignItems: "center",
            p: 2,
            flexShrink: 0,
            gap: 2,
          }}
        >
          {currentStep !== 0 && (
            <Button
              onClick={handlePrevious}
              variant="outlined"
              color="secondary"
            >
              Previous
            </Button>
          )}

          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            {currentStep === sections?.length - 1 ? (
              <></>
            ) : (
              <>
                <Button color="primary" onClick={handleSaveProgress}>
                  Save&nbsp;Progress
                </Button>
              </>
            )}
            {currentStep === sections?.length - 1 ? (
              <Button
                onClick={handleSubmit}
                variant="contained"
                color="primary"
              >
                Submit
              </Button>
            ) : (
              <Button onClick={handleNext} variant="contained" color="primary">
                Next
              </Button>
            )}
          </Box>
        </Box>
      </Box>

      <Dialog
        open={dialogSuccess}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleCloseDialog}
              sx={{ margin: "auto", width: 100 }}
            >
              Ok
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default LongFormContent;
