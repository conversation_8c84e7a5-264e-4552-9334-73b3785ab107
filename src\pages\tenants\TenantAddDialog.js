import { useContext, useState } from "react";

// ** Custom Components Imports

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component

import { AuthContext } from "src/context/AuthContext";
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  TextField,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import axios from "axios";
import { getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";

const TenantAddDialog = ({ open, onClose,fetchTenants,page,pageSize,searchKeyword }) => {
  const theme = useTheme();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    clearErrors,
    control,
    reset,
    formState: { errors },
  } = useForm();

  const [saveLoading, setSaveLoading] = useState(false);
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const handleClose = () => setOpenDialogContent(false);

  const handleSuccess = () => {
    const message = `
        <div> 
          <h3> Tenant added Successfully.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (err) => {
    let message;
    if (err?.response?.status == 400) {
      message = `
        <div>
          <h3>Tenant already exists!</h3>
        </div>
      `;
    } else {
      message = `
        <div> 
          <h3> Failed to Add Tenant. Please try again later.</h3>
        </div>
      `;
    }

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  async function submit(data) {
    const ipAddress = await fetchIpAddress();
    setSaveLoading(true);
    const fields = {
      trustName: data?.trustName,
      contactPersonName: data?.contactName,
      mobileNumber:data?.mobileNumber,
      email:data?.email,
      ipAddress: ipAddress,
      website: data?.websiteUrl,
      roleId:authConfig.tenantAdminRoleId,
      orgEmail:data?.orgEmail
    }
    axios({
      method: "post",
      url: getUrl(authConfig.signUpEndpoint),
      data: fields,
    })
      .then((res) => {
        handleSuccess(); 
        handleCloseDialog();
        fetchTenants(page,pageSize,searchKeyword);
      })
      .catch((err) => {
        handleFailure();
        handleCloseDialog();
      });
   
    setSaveLoading(false);
  }

  const handleCloseDialog = () => {
    setValue("trustName", "");
    setValue("orgEmail", "");
    setValue("contactName", "");
    setValue("email", "");
    setValue("mobileNumber", "");
    setValue("websiteUrl", "");
    onClose(); // Close the dialog
  };

  return (
    <>
      <Dialog open={open} onClose={handleCloseDialog} maxWidth="xs">
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            height: "50px", // Set fixed height for header
          }}
          textAlign={"center"}
        >
          <Box
            sx={{
              fontSize: {
                xs: 14, // Smaller font size for mobile
                sm: 15, // Slightly larger font size
                md: 17, // Default font size for larger screens
                lg: 16,
              },
              fontWeight: 600, // Bold text if needed
              ml: {
                xs: 3,
                xl: 3,
              },
            }}
          >
            Add New Tenant
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "10px",
              mr: {
                xs: 5.5,
                sm: 5.5,
                md: 5.5,
                lg: 5.5,
                xl: 5.5,
              },
            }}
          >
            <IconButton
              size="small"
              onClick={handleCloseDialog}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#9a9ae5",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            p: (theme) => `${theme.spacing(6, 8)} !important`,
          }}
        >
          <Grid container spacing={2}>
            <Grid item xs={12} xl={12}>
              <FormControl fullWidth>
                <Controller
                  name="trustName"
                  control={control}
                  rules={{ required: "Trust Name is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Trust Name*"
                      placeholder="Enter Trust Name"
                      fullWidth
                      size="small"
                      onChange={(e) => {
                        const newValue = e.target.value.replace(/\s/g, " ");
                        field.onChange(newValue);
                      }}
                      id="trust-name"
                      error={Boolean(errors.trustName)}
                      helperText={errors.trustName?.message}
                      InputLabelProps={{
                        shrink: true,
                        sx: { fontSize: "1rem" },
                      }}
                      sx={{
                        borderRadius: "5px",
                        background: "white",
                        width: "100%",
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} xl={12}>
              <Controller
                name="contactName"
                control={control}
                rules={{ required: "Contact Person Name is required" }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Contact Person Name*"
                    placeholder="Enter Contact Person Name"
                    fullWidth
                    size="small"
                    onChange={(e) => {
                      const newValue = e.target.value.replace(/\s/g, " ");
                      field.onChange(newValue);
                    }}
                    id="contact-name"
                    error={Boolean(errors.contactName)}
                    helperText={errors.contactName?.message}
                    InputLabelProps={{
                      shrink: true,
                      sx: { fontSize: "1rem" },
                    }}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} xl={12}>
              <FormControl fullWidth>
                <Controller
                  name="email"
                  control={control}
                  rules={{
                    required: "Email is required",
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                      message: "Enter a valid email address",
                    },
                    minLength: {
                      value: 8,
                      message: "Email must be at least 8 characters long",
                    },
                    maxLength: {
                      value: 100,
                      message: "Email cannot be longer than 100 characters",
                    },
                    validate: async (value) => {
                      try {
                        const res = await axios.post(
                          getUrl(authConfig.individualVerificationAudit) +
                            "/check-email",
                          { email: value }
                        );
                        if (res?.data?.message === "Email Already Exist") {
                          return "Email Already Exist";
                        }
                        return true;
                      } catch (err) {
                        return "Failed to validate email";
                      }
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      type="email"
                      {...field}
                      label="Contact Person Email*"
                      size="small"
                      InputLabelProps={{
                        shrink: true,
                        sx: { fontSize: "1rem" },
                      }}
                      helperText={errors.email?.message}
                      error={Boolean(errors.email)}
                      placeholder="Email requires verification"
                      fullWidth
                      inputProps={{
                        minLength: 8,
                        maxLength: 100,
                      }}
                      onChange={(event) => {
                        if (event.target.value.length <= 100) {
                          field.onChange(event);
                          // setEmail(event.target.value);
                        }
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} xl={12}>
              <FormControl fullWidth>
                <Controller
                  name="mobileNumber"
                  control={control}
                  rules={{ required: "Mobile Number is required" }}
                  render={({ field }) => (
                    <MobileNumberValidation
                      {...field}
                      type="tel"
                      label="Contact Person Number*"
                      size="small"
                      error={Boolean(errors.mobileNumber)}
                      helperText={errors.mobileNumber?.message}
                      InputLabelProps={{ shrink: true }}
                      placeholder="+91 1234567890"
                      inputProps={{
                        maxLength: field?.value?.startsWith("+91") ? 13 : 10,
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} xl={12}>
              <FormControl fullWidth>
                <Controller
                  name="orgEmail"
                  control={control}
                  rules={{
                    required: "Organisation Email is required",
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                      message: "Enter a valid email address",
                    },
                    minLength: {
                      value: 8,
                      message: "Email must be at least 8 characters long",
                    },
                    maxLength: {
                      value: 100,
                      message: "Email cannot be longer than 100 characters",
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      type="email"
                      {...field}
                      label="Organisation Email*"
                      size="small"
                      InputLabelProps={{
                        shrink: true,
                        sx: { fontSize: "1rem" },
                      }}
                      helperText={errors.orgEmail?.message}
                      error={Boolean(errors.orgEmail)}
                      placeholder="Email cannot be changed later"
                      fullWidth
                      inputProps={{
                        minLength: 8,
                        maxLength: 100,
                      }}
                      onChange={(event) => {
                        if (event.target.value.length <= 100) {
                          field.onChange(event);
                        }
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} xl={12}>
              <FormControl fullWidth>
                <Controller
                  name="websiteUrl"
                  control={control}
                  rules={{ pattern: /^(ftp|http|https):\/\/[^ "]+$/ }}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      type="url"
                      value={value}
                      size="small"
                      label="Website"
                      onChange={onChange}
                      error={Boolean(errors.websiteUrl)}
                      placeholder="https://www.example.com"
                      aria-describedby="validation-websiteUrl"
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5, 4)} !important`,
            height: "50px", // Set fixed height for footer
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={() => {
              handleCloseDialog();
            }}
          >
            Cancel
          </Button>

          <Button
            display="flex"
            justifyContent="center"
            variant="contained"
            color="primary"
            onClick={handleSubmit(submit)}
            sx={{
              mr: {
                xs: 4,
                sm: 4,
                md: 4,
                lg: 4,
                xl: 4,
              },
            }}
          >
            {saveLoading ? (
              <CircularProgress color="inherit" size={22} />
            ) : (
              "Save"
            )}
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openDialogContent}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default TenantAddDialog;
