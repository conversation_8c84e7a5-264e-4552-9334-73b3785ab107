import PhoneAndroidIcon from "@mui/icons-material/PhoneAndroid";
import {
  Box,
  Button,
  CircularProgress,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Divider,
  FormControl,
  IconButton,
  InputAdornment,
  Menu,
  MenuItem,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { useContext, useState } from "react";
// ** Layout Import
import { useTheme } from "@mui/material/styles";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import BlankLayout from "src/@core/layouts/BlankLayout";
import authConfig from "src/configs/auth";
import { useRouter } from "next/router";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";
import axios from "axios";
import { getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { AuthContext } from "src/context/AuthContext";

import FallbackSpinner from "src/@core/components/spinner";

// ** Next Imports

const LoginPage = () => {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const router = useRouter();
  const auth = useAuth();

  const { loginLoad } = useContext(AuthContext);

  const theme = useTheme();
  const [overrideExistingLogins, setOverrideExistingLogins] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [isFailed, setIsFailed] = useState(false);
  const [showPopup, setShowPopup] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);

  const handleFailure = () => {
    setShowPopup(false);
    setIsFailed(true);
    const message = ` 
    <div>
      <h3>Email or password credentials are invalid.</h3>
    </div>
    `;
    setDialogMessage(message);
    setOpenDialog(true);
  };

  const handleSuccess = () => {
    setShowPopup(true);
    setIsFailed(false);
    const message = `
    <div>
      <h2>Login Successful.</h2>
      <h5>Redirecting to your dashboard.</h5>
    </div>
`;
    setDialogMessage(message);
    setOpenDialog(true);
  };

  const handlePopup = () => {
    setIsFailed(false);
    setShowPopup(true);
    const message = ` 
    <div>
    <h3>You are currently logged in on multiple devices. Do you want to log out from all other devices</h3>
    </div>
    `;
    setDialogMessage(message);
    setOpenDialog(true);
  };

  async function submit(data) {
    const ipAddress = await fetchIpAddress();
    const fields = {
      email: data?.email,
      password: data?.password,
      ipAddress: ipAddress,
      overrideExistingLogins: overrideExistingLogins,
    };

    try {
      await auth.loginNew(fields, handleFailure, handleSuccess, handlePopup);
    } catch (error) {
      // Handle any errors from validation, IP fetch, or login process
      console.error("An error occurred:", error);
      handleFailure()
    }

  }

  const handleYes = async (data) => {
    setOverrideExistingLogins(true);
    const ipAddress = await fetchIpAddress();
    const fields = {
      email: data?.email,
      password: data?.password,
      ipAddress: ipAddress,
      overrideExistingLogins: true,
    };

      await auth.loginNew(fields, handleFailure, handleSuccess, handlePopup);
   
  };

  const handleOpenMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };
  const handleClose = () => {
    setOpenDialog(false);
  };

  const handleChange = () => {
    setOverrideExistingLogins(true);
  };

  const handleClick = (role) => {
    router.push(`/register?role=${role}`);
  };

  const handleForgotPassword = () => {
    router.push("/forgot-password");
  };

   const handleGoogleClick = () => {
      const url = authConfig.googleAuthUrl;
      window.location.href = url;
    };

  return (
    loginLoad ? (
      <FallbackSpinner />
    ):(
      <>
      <Box 
        sx={{
          display: { xs: "flex", sm: "block" }, 
          justifyContent: { xs: "center" }, 
          alignItems: { xs: "center" }, 
          height: { xs: "100vh" }, 
          transform: {
            xs: "scale(0.9)", 
            sm: "none", 
          },
        }}
      >
        <Container
          maxWidth="xs"
          sx={{
            height: "90vh",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "space-between",
            boxShadow: 3,
            p: 4,
            borderRadius: 6,
            bgcolor: "white",
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            overflowY: "auto",
          }}
        >
          <Box  sx={{  width: "80%" }}>
            <Typography
              variant="h5"
              fontWeight="450"
              sx={{
                ml: 0,
                mb: 4,
              }}
            >
              Log In
            </Typography>
            <Typography
              variant="h6"
              fontWeight="450"
              sx={{
                fontSize: { xs: "15px", lg: "20px", sm: "18px" },
                mt: 2,
                mb:6,
                display: "flex",
                alignItems: "center",
                // gap: "8px",
              }}
            >
              <a href={authConfig.guestURL + "home"}>
                <img
                  src="/images/donation-reciept/logo-1.webp"
                  alt="Donation Receipt Logo"
                  style={{width: "50px", height: "45px" }}
                />
              </a>
              Welcome to Donation Receipt! 👋🏻
            </Typography>
  
           
         
            <Stack spacing={2} >
              <FormControl fullWidth>
                <Controller
                  name="email"
                  control={control}
                  rules={{
                    required: "Email is required",
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                      message: "Enter a valid email address",
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Email*"
                      size="small"
                      InputLabelProps={{ shrink: true, sx: { fontSize: "1rem" } }}
                      helperText={errors.email?.message}
                      error={Boolean(errors.email)}
                      placeholder="<EMAIL>"
                      fullWidth
                    />
                  )}
                />
              </FormControl>
              <FormControl fullWidth>
                <Controller
                  name="password"
                  control={control}
                  rules={{
                    required: "Password is required",
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Password*"
                      size="small"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter Password"
                      InputLabelProps={{ shrink: true, sx: { fontSize: "1rem" } }}
                      helperText={errors.password?.message}
                      error={Boolean(errors.password)}
                      fullWidth
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              edge="end"
                              onMouseDown={(e) => e.preventDefault()}
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              <Icon
                                icon={
                                  showPassword ? "tabler:eye" : "tabler:eye-off"
                                }
                                fontSize={{ xs: 5, lg: 20 }}
                              />
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
              </FormControl>
            </Stack>
  
           
            <Box
              sx={{
                display: "flex",
                flexWrap: "wrap",
              }}
            >
              <Typography
                sx={{
                  color: "text.secondary",
                  fontSize: { xs: "0.5rem", lg: "0.8rem" },
                  mb: 1,
                }}
              >
                <span
                  onClick={handleForgotPassword}
                  style={{
                    cursor: "pointer",
                    fontWeight: 500,
                    textDecoration: "none",
                    color: theme.palette.primary.main,
                  }}
                >
                  Forgot Password ?
                </span>
              </Typography>
            </Box>
  
            <Box
              sx={{
                display: "flex",
                flexWrap: "wrap",
                alignItems: "center",
              }}
            >
              <Typography
                sx={{
                  color: "text.secondary",
                  mr: 2,
                  fontSize: { xs: "0.7rem", lg: "0.9rem" },
                }}
              >
                New on Donation Receipt?{" "}
              </Typography>
              <Button
                onClick={handleOpenMenu}
                sx={{
                  color: theme.palette.primary.main,
                  fontSize: { xs: "0.7rem", lg: "0.9rem" },
                  fontWeight: 500,
                  textTransform: "none",
                  p: 0,
                  minWidth: "auto",
                  border: "none",
                  "&:hover": {
                    backgroundColor: "transparent",
                    border: "none",
                  },
                  "&:focus": {
                    outline: "none",
                    border: "none",
                  }
                }}
              >
                Create Account
              </Button>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleCloseMenu}
              >
                <MenuItem onClick={() => {
                  handleCloseMenu();
                  handleClick("ngo");
                }}>
                  Sign Up as NGO
                </MenuItem>
                <MenuItem onClick={() => {
                  handleCloseMenu();
                  handleClick("donor");
                }}>
                  Sign Up as Donor
                </MenuItem>
              </Menu>
            </Box>
          </Box>
          <Box
            sx={{
              width: "80%",
              py: 2,
              textAlign: "center",
              mt: "auto",
            }}
          >
            <>
              <Button
                variant="contained"
                color="primary"
                sx={{ mt: 4, width: "100%" }}
                onClick={handleSubmit(submit)}
              >
                Login
              </Button>
            </>
          </Box>
        </Container>
        </Box>
        <Dialog
          open={openDialog}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            {isFailed && (
              <>
                <DialogActions>
                  <Button
                    onClick={handleClose}
                    style={{ margin: "10px auto", width: 100 }}
                  >
                    Okay
                  </Button>
                </DialogActions>
              </>
            )}
            {showPopup &&
              dialogMessage &&
              !dialogMessage.includes("Login Successful.") && (
                <>
                  <Button
                    onClick={handleSubmit(handleYes)}
                    checked={overrideExistingLogins}
                    onChange={handleChange}
                    style={{ margin: "10px auto", width: 100 }}
                  >
                    Yes
                  </Button>
                  <Button
                    onClick={handleClose}
                    style={{ margin: "10px auto 10px 20px", width: 100 }}
                  >
                    No
                  </Button>
                </>
              )}
            {showPopup &&
              dialogMessage &&
              dialogMessage.includes("Login Successful.") && (
                <>
                  <Button
                    style={{
                      margin: "10px auto",
                      display: "block",
                      width: 100,
                    }}
                  >
                    <CircularProgress color="inherit" size={24} />
                  </Button>
                </>
              )}
          </Box>
        </Dialog>
      </>
    )
   
  );
};

LoginPage.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
LoginPage.guestGuard = true;

export default LoginPage;