// ** MUI Components
import MuiLink from '@mui/material/Link'
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box'
import useMediaQuery from '@mui/material/useMediaQuery'
import authConfig from 'src/configs/auth';


const Footer = () => {
  const hidden = useMediaQuery(theme => theme.breakpoints.down('md'))

  const localhost = authConfig.guestURL;

  return (
    <>
      <Box sx={{ p: 5, display: 'flex', flexWrap: 'wrap', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography sx={{ mr: 2 }}>
          {`© ${new Date().getFullYear()} `}
          <MuiLink target='_blank' href='/dashboard'>
            Donation Receipt
          </MuiLink>
        </Typography>
        {hidden ? null : (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center', '& :not(:last-child)': { mr: 4 } }}>
            <MuiLink href={localhost + 'contact-us'}>Contact Us</MuiLink>
            <MuiLink href={localhost + 'privacy-policy'}>Privacy Policy</MuiLink>
            <MuiLink href={localhost + 'terms-conditions'}>Terms & Conditions</MuiLink>
          </Box>
        )}
      </Box>
    </>
  )
}

export default Footer
