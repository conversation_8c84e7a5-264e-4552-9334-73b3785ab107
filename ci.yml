name: CI Pipeline

on:
  push:
    branches: [ "main", "donation/*" ]
  pull_request:
    branches: [ "main" ]

jobs:
  build:

    runs-on: ${{matrix.os}}

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macOs-latest]
        node-version: [20.x, 22.x]
      fail-fast: false

    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    - run: npm ci # install dependencies
    - run: npm run build --if-present # build the project