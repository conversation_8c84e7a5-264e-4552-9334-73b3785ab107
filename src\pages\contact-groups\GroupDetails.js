import React, { useEffect, useRef } from 'react';
import {
  Box,
  FormControl,
  FormHelperText,
  Grid,
  TextField,
  Typography
} from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import SelectAutoComplete from 'src/@core/components/custom-components/SelectAutoComplete';

const GroupDetails = ({ formData, onUpdate, tenantsList, tenantId, setTenantId, user }) => {
  const {
    control,
    watch,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: formData?.profileDetails || {}, // Initialize form fields with existing data
  });

  // Ref to track if the initial reset has been called
  const isInitialized = useRef(false);

  // Reinitialize form when formData.profileDetails changes
  useEffect(() => {
    if (formData?.profileDetails && !isInitialized.current) {
      reset(formData.profileDetails); // Reset the form with initial values
      isInitialized.current = true; // Mark as initialized
    }
  }, [formData?.profileDetails, reset]);

  // Watch all fields for changes
  const watchedFields = watch();
  // Update formData on any change
  const previousWatchedFields = useRef();

  useEffect(() => {
    // Compare previous watched fields with current watched fields
    const hasWatchedFieldsChanged =
      JSON.stringify(previousWatchedFields.current) !==
      JSON.stringify(watchedFields);

    if (hasWatchedFieldsChanged) {
      onUpdate({
        ...watchedFields,
      });
      previousWatchedFields.current = watchedFields; // Update the ref with the latest value
    }
  }, [watchedFields, onUpdate]);

  return (
    <Box sx={{ pt: 3 }}>
      <Typography variant="h6" sx={{ mb: 4 }}>
        Enter Group Details
      </Typography>
      
      <Grid container spacing={5} sx={{ p: 2 }}>
        {user?.organisationCategory === "SUPER_ADMIN" && (
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth error={Boolean(errors.tenantName)}>
              <Controller
                name="tenantName"
                control={control}
                rules={{
                  required: tenantId ? false : "Tenant Name is required",
                }}
                render={({ field }) => (
                  <SelectAutoComplete
                    id="tenantName"
                    label="Tenant Name"
                    nameArray={tenantsList}
                    value={tenantId}
                    onChange={(event) => {
                      field.onChange(event.target?.value);
                      setTenantId(event.target?.value);
                    }}
                  />
                )}
              />
              {errors.tenantName && (
                <FormHelperText sx={{ color: "error.main" }}>
                  {errors.tenantName.message}
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
        )}
        
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth error={Boolean(errors.contactGroupName)}>
            <Controller
              name="contactGroupName"
              control={control}
              rules={{ required: "Group Name is required" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  size='small'
                  label="Group Name"
                  placeholder="Enter group name"
                  error={Boolean(errors.contactGroupName)}
                  helperText={errors.contactGroupName?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
        
        <Grid item xs={12}>
          <FormControl fullWidth>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  size='small'
                  label="Description"
                  placeholder="Enter group description"
                  multiline
                  rows={4}
                />
              )}
            />
          </FormControl>
        </Grid>
      </Grid>
    </Box>
  );
};

export default GroupDetails;
