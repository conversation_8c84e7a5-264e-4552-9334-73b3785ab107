import {
  Alert,
  Box,
  Button,
  CircularProgress,
  Container,
  FormControl,
  Grid,
  Snackbar,
  TextField,
  Typography,
  MenuItem,
  Select,
  InputLabel,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Divider,
  FormHelperText,
} from "@mui/material";
import { useContext, useState, useEffect } from "react";
// ** Layout Import
import { styled } from "@mui/material/styles";
import { Controller, useForm } from "react-hook-form";
import BlankLayout from "src/@core/layouts/BlankLayout";
import authConfig from "src/configs/auth";
// ** Next Imports
import Link from "next/link";
import CustomChip from "src/@core/components/mui/chip";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import FallbackSpinner from "src/@core/components/spinner";
import { AuthContext } from "src/context/AuthContext";
import axios from "axios";
import { getUrl } from "src/helpers/utils";
import { useRouter } from "next/router";
import CloseIcon from "@mui/icons-material/Close";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";

const LinkStyled = styled(Link)(({ theme }) => ({
  fontSize: { xs: "0.7rem", lg: "0.9rem" },
  textDecoration: "none",
  color: theme.palette.primary.main,
}));

const OtpDialog = ({
  open,
  onClose,
  contact, // email or mobile
  onValidate,
  onResend,
  countdown,
  setOtpValue,
  otpValue,
  loading,
}) => (
  <Dialog open={open} onClose={onClose} maxWidth="xs" fullWidth>
    <DialogTitle
      sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        pt: 1,
        pb: 1.5,
      }}
    >
      Verify OTP
      <IconButton onClick={onClose}>
        <CloseIcon />
      </IconButton>
    </DialogTitle>
    <Divider />
    <DialogContent>
      <Typography sx={{ color: "#1A1F2B", mb: 2, textAlign: "center" }}>
        OTP has been sent to <b>{contact}</b> for verification. Please check.
      </Typography>
      <Box display="flex" justifyContent="center" mb={2}>
        <TextField
          label="OTP"
          type="number"
          value={otpValue}
          onChange={(e) => setOtpValue(e.target.value)}
          size="small"
          sx={{ width: 200 }}
          inputProps={{ maxLength: 6 }}
        />
      </Box>
      <Box display="flex" justifyContent="center" gap={2}>
        <Button
          id="validateOtp"
          variant="contained"
          disabled={!otpValue}
          onClick={onValidate}
          sx={{
            marginBottom: "16px",
            "&:disabled": { color: "primary.main" },
          }}
        >
          VALIDATE OTP
        </Button>
        <Button
          id="resendOtp"
          variant={countdown > 0 ? "outlined" : "contained"}
          disabled={countdown > 0}
          onClick={onResend}
          sx={{
            marginLeft: "7px",
            marginBottom: "16px",
            "&:disabled": { color: "primary.main" },
          }}
        >
          RESEND OTP
        </Button>
      </Box>
      {countdown > 0 && (
        <Typography
          variant="body1"
          sx={{
            marginTop: "2px",
            marginBottom: "10px",
            color: "primary.main",
          }}
        >
          Resend OTP in: {countdown}s
        </Typography>
      )}
    </DialogContent>
  </Dialog>
);

const SignupPage = () => {
  const {
    control,
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });

  const router = useRouter();
  const { role } = router.query; // Get the role from URL query parameter

  const { pageLoad } = useContext(AuthContext);

  const [loading, setLoading] = useState(false); // State for loading spinner

  const [showToast, setShowToast] = useState(false); // State for toast visibility
  const [toastMessage, setToastMessage] = useState("");

  const [mobileVerified, setMobileVerified] = useState(false);
  const [loadingMobileVerify, setLoadingMobileVerify] = useState(false);
  const [tempMobile, setTempMobile] = useState("");
  const [showMobileOtpDialog, setShowMobileOtpDialog] = useState(false);

  const [name, setName] = useState("");
  const [mobileNumber, setMobileNumber] = useState("");
  const [showOtpDialog, setShowOtpDialog] = useState(false);
  const [otpValue, setOtpValue] = useState("");
  const [countdown, setCountdown] = useState(0);
  const [verifyingContact, setVerifyingContact] = useState(""); // email or mobile
  const [loadingOtp, setLoadingOtp] = useState(false);
  const [selectedReferralSourceId, setSelectedReferralSourceId] =
    useState(null);
  const [referralSourceOptionsData, setReferralSourceOptionsData] = useState(
    []
  );
  const [referralSourceRegisteredName, setReferralSourceRegisteredName] =
    useState("");
  const { getAllListValuesByListNameId } = useContext(AuthContext);
  const [donorTypeOptionsData, setDonorTypeOptionsData] = useState([]);
  const [selectedDonorTypeId, setSelectedDonorTypeId] = useState(null);
  const [donorTypeRegisteredName, setDonorTypeRegisteredName] = useState("");

  const handleToastClose = () => {
    setShowToast(false);
    setToastMessage("");
  };

  async function submit(data) {
    const ipAddress = await fetchIpAddress();
    const payload = Object.entries(data).reduce((acc, [key, value]) => {
      if (value != null) acc[key] = value; // filters out null and undefined
      return acc;
    }, {});
    payload.ipAddress = ipAddress;
    if (role === "donor") {
      payload.roleId = authConfig.donorRoleId;
    } else {
      payload.roleId = authConfig.tenantAdminRoleId;
    }
    setLoading(true); // Show loading spinner
    axios({
      method: "post",
      url: getUrl(authConfig.signUpEndpoint),
      data: payload,
    })
      .then((res) => {
        router.replace("/register/activate");
        setLoading(false); // Hide loading spinner
      })
      .catch((err) => console.log("Sign Up error", err));
  }

  const handleError = (error) => {
    console.error("Donor profile: All Services:", error);
  };

  useEffect(() => {
    getAllListValuesByListNameId(
      authConfig.referralSourceListNameId,
      (data) =>
        setReferralSourceOptionsData(
          data?.listValues?.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );
    getAllListValuesByListNameId(
      authConfig.donorTypeListNameId,
      (data) =>
        setDonorTypeOptionsData(
          data?.listValues?.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );
  }, []);

  const handleReferralSourceChange = (event) => {
    const selectedId = event.target.value;
    setSelectedReferralSourceId(selectedId);
    const referralSourceRegistered = referralSourceOptionsData.find(
      (referralRegistered) => referralRegistered?.value === selectedId
    );

    setReferralSourceRegisteredName(referralSourceRegistered?.key);

    if (referralSourceRegistered?.key === "any other") {
      setValue("donorReferralSourceAnyOther", ""); // update form value
    } else {
      setValue("donorReferralSourceAnyOther", null); // reset form value
    }
  };

  const handleDonorTypeChange = (event) => {
    const selectedId = event.target.value;
    setSelectedDonorTypeId(selectedId);
    const DonorTypeRegistered = donorTypeOptionsData.find(
      (DonorRegistered) => DonorRegistered?.value === selectedId
    );

    setDonorTypeRegisteredName(DonorTypeRegistered?.key);

    if (DonorTypeRegistered?.key === "any other") {
      setValue("anyOtherDonorTypeRegistered", ""); // update form value
    } else {
      setValue("anyOtherDonorTypeRegistered", null); // reset form value
    }
  };

  const handleMobileVerification = async (data) => {
    setLoadingMobileVerify(true);
    // Here, send OTP to mobile (API or mock)
    setShowMobileOtpDialog(true); // Show OTP dialog
    setLoadingMobileVerify(false);
  };

  useEffect(() => {
    if (countdown > 0) {
      const timerId = setTimeout(() => setCountdown(countdown - 1), 1000);

      return () => clearTimeout(timerId);
    }
  }, [countdown]);

  async function handleVerifyClick(mobile) {
    const fields = {
      name: name,
      roleId:
        role === "donor"
          ? authConfig.donorRoleId
          : authConfig.tenantAdminRoleId,
    };

    axios({
      method: "post",
      url:
        getUrl(authConfig.mobileOTPEndpoint) + "/send/?mobile=" + mobileNumber,
      data: fields,
    })
      .then((res) => {
        setShowOtpDialog(true);
        setCountdown(30);
      })
      .catch((err) => {
        console.log("error", err);
      });
  }

  const handleValidateOtp = () => {
    setLoadingOtp(true);
    axios({
      method: "get",
      url:
        getUrl(authConfig.mobileOTPEndpoint) +
        "/verify/?mobile=" +
        mobileNumber +
        "&otp=" +
        otpValue,
    })
      .then((res) => {
        setLoadingOtp(false);
        setShowOtpDialog(false);
        setMobileVerified(true);
        setOtpValue("");
      })
      .catch((err) => {
        console.log("error", err);
      });
  };

  const handleResendOtp = () => {
    axios({
      method: "get",
      url:
        getUrl(authConfig.mobileOTPEndpoint) +
        "/resend/?mobile=" +
        mobileNumber,
    })
      .then((res) => {
        setCountdown(30);
      })
      .catch((err) => {
        console.log("error", err);
      });
  };

  const isValidName = name && name.trim().length > 0;
  const isValidMobile = mobileNumber && /^\d{10}$/.test(mobileNumber); // assuming 10-digit number
  const isDisabled = !isValidName || !isValidMobile;

  return pageLoad ? (
    <FallbackSpinner />
  ) : (
    <>
      <Container
        maxWidth="xs"
        sx={{
          height: "90vh",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "start",
          boxShadow: 3,
          p: 8,
          mt: 8,
          mb: 8,
          borderRadius: 6,
          bgcolor: "white",
          position: "relative",
          overflowY: "auto",
          flexGrow: 1,
        }}
      >
        <Box
          sx={{
            width: "80%",
            display: "flex",
            flexDirection: "column",
            flex: 1,
            justifyContent: "top",
          }}
        >
          <Typography variant="h5" fontWeight="450" sx={{ ml: 0, mb: 1.5 }}>
            {role === "donor" ? "Donor Sign Up" : "Sign Up"}
          </Typography>

          <Typography
            variant="h6"
            fontWeight="500"
            sx={{
              fontSize: { xs: "15px", lg: "20px", sm: "18px" },
              mt: 2,
              ml: -2,
              display: "flex",
              alignItems: "center",
            }}
          >
            <a href={authConfig.guestURL + "home"}>
              <img
                src="/images/donation-reciept/logo-1.webp"
                alt="Houzer Logo"
                style={{ width: "50px", height: "45px" }} // Increased size
              />
            </a>
            Welcome to Donation Receipt! 👋🏻
          </Typography>
          <Box>
            <Typography variant="body2" sx={{ my: 2 }}>
              {""}
            </Typography>

            <Grid
              container
              spacing={3}
              sx={{
                marginTop: 4,
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              {role === "donor" ? (
                <>
                  <Grid item xs={12}>
                    <Controller
                      name="donorName"
                      control={control}
                      rules={{ required: "Name is required" }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Name*"
                          placeholder="Enter your name"
                          fullWidth
                          size="small"
                          value={name}
                          onChange={(event) => {
                            field.onChange(event.target.value); 
                            setName(event.target.value);
                          }}
                          error={Boolean(errors.donorName)}
                          helperText={errors.donorName?.message}
                          InputLabelProps={{
                            shrink: true,
                            sx: { fontSize: "1rem" },
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Controller
                      name="email"
                      control={control}
                      rules={{
                        required: "Email is required",
                        pattern: {
                          value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                          message: "Enter a valid email address",
                        },
                        minLength: {
                          value: 8,
                          message: "Email must be at least 8 characters long",
                        },
                        maxLength: {
                          value: 100,
                          message: "Email cannot be longer than 100 characters",
                        },
                        validate: async (value) => {
                          try {
                            const res = await axios.post(
                              getUrl(authConfig.individualVerificationAudit) +
                                "/check-email",
                              { email: value }
                            );
                            if (res?.data?.message === "Email Already Exist") {
                              return "Email Already Exist";
                            }
                            return true;
                          } catch (err) {
                            return "Failed to validate email";
                          }
                        },
                      }}
                      render={({ field }) => (
                        <TextField
                          type="email"
                          {...field}
                          label="Email*"
                          size="small"
                          InputLabelProps={{
                            shrink: true,
                            sx: { fontSize: "1rem" },
                          }}
                          helperText={errors.email?.message}
                          error={Boolean(errors.email)}
                          placeholder="Enter your email"
                          fullWidth
                          inputProps={{
                            minLength: 8,
                            maxLength: 100,
                          }}
                          onChange={(event) => {
                            if (event.target.value.length <= 100) {
                              field.onChange(event);
                            }
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <FormControl sx={{ width: "100%" }}>
                      <Box display="flex" alignItems="center" width="100%">
                        <Controller
                          name="mobileNumber"
                          control={control}
                          rules={{ required: "Mobile number is required" }}
                          render={({ field }) => (
                            <MobileNumberValidation
                              {...field}
                              label="Mobile Number*"
                              size="small"
                              InputLabelProps={{ shrink: true }}
                              value={mobileNumber}
                              onChange={(event) => {
                                 field.onChange(event); 
                                setMobileNumber(event);
                              }}
                              helperText={errors.mobileNumber?.message}
                              error={Boolean(errors.mobileNumber)}
                              placeholder="Enter your mobile number"
                              fullWidth
                              disabled={mobileVerified}
                              sx={{
                                "& .MuiInputBase-input::placeholder": {
                                  fontSize: "1rem",
                                  "@media (max-width:600px)": {
                                    fontSize: "0.75rem",
                                  },
                                },
                                "& .MuiInputBase-input": {
                                  padding: "8px",
                                  fontSize: "1rem",
                                  "@media (max-width:600px)": {
                                    padding: "6px",
                                    fontSize: "0.75rem",
                                  },
                                },
                                "& .MuiInputLabel-root": {
                                  fontSize: "1.0rem",
                                  "@media (max-width:600px)": {
                                    fontSize: "0.75rem",
                                  },
                                },
                              }}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="end">
                                    {mobileVerified ? (
                                      <CustomChip
                                        rounded={true}
                                        skin="light"
                                        size="small"
                                        label={
                                          <span style={{ fontSize: "inherit" }}>
                                            Verified
                                          </span>
                                        }
                                        color="success"
                                        sx={{
                                          textTransform: "capitalize",
                                          mr: { xs: 0, lg: 0 },
                                          fontSize: {
                                            xs: "0.7rem",
                                            lg: "0.8rem",
                                          },
                                        }}
                                      />
                                    ) : (
                                      <Box
                                        onClick={() => {
                                          if (!isDisabled) {
                                            handleVerifyClick(field.value);
                                          }
                                        }}
                                        sx={{
                                          border: "1px solid #f2f7f2",
                                          borderRadius: "4px",
                                          padding: "0 5px",
                                          cursor: isDisabled
                                            ? "not-allowed"
                                            : "pointer",
                                          color: isDisabled ? "gray" : "blue",
                                          opacity: isDisabled ? 0.6 : 1,
                                          pointerEvents: isDisabled
                                            ? "none"
                                            : "auto",
                                        }}
                                      >
                                        <Typography
                                          variant="caption"
                                          sx={{
                                            fontSize: {
                                              xs: "0.7rem",
                                              lg: "0.8rem",
                                            },
                                            color: isDisabled ? "gray" : "blue",
                                          }}
                                        >
                                          {loadingOtp ? (
                                            <CircularProgress
                                              color="inherit"
                                              size={22}
                                            />
                                          ) : (
                                            "Verify"
                                          )}
                                        </Typography>
                                      </Box>
                                    )}
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </Box>
                    </FormControl>
                  </Grid>

                  {/* Donor Type */}
                  <Grid item xs={12}>
                    <FormControl fullWidth error={Boolean(errors.donorType)}>
                      <Controller
                        name="donorType"
                        control={control}
                        rules={{ required: "Donor type is required" }}
                        render={({ field }) => (
                          <SelectAutoComplete
                            size="small"
                            labelId="donor-type-label"
                            label="Donor Type"
                            nameArray={donorTypeOptionsData}
                            value={selectedDonorTypeId}
                            onChange={(e) => {
                              field.onChange(e);
                              handleDonorTypeChange(e);
                            }}
                          />
                        )}
                      />
                      {errors.donorType && (
                        <FormHelperText sx={{ color: "error.main" }}>
                          {errors.donorType.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  {donorTypeRegisteredName === "Entity" && (
                    <Grid item xs={12}>
                      <Controller
                        name="donorOrgName"
                        control={control}
                        rules={{
                          required: "Organization Name is required",
                          maxLength: {
                            value: 100,
                            message:
                              "Organization Name cannot exceed 50 characters",
                          },
                        }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Organization Name*"
                            placeholder="Enter your organization name"
                            fullWidth
                            size="small"
                            error={Boolean(errors.donorOrgName)}
                            helperText={errors.donorOrgName?.message}
                            inputProps={{ maxLength: 100 }}
                          />
                        )}
                      />
                    </Grid>
                  )}

                  <Grid item xs={12}>
                    <Controller
                      name="donorPanNo"
                      control={control}
                      rules={{
                        required: "PAN number is required",
                        pattern: {
                          value: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
                          message:
                            "Please enter a valid PAN number (e.g., **********)",
                        },
                        minLength: {
                          value: 10,
                          message: "PAN number must be 10 characters",
                        },
                        maxLength: {
                          value: 10,
                          message: "PAN number must be 10 characters",
                        },
                      }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="PAN Number"
                          fullWidth
                          size="small"
                          error={Boolean(errors.donorPanNo)}
                          helperText={errors.donorPanNo?.message}
                          placeholder="Enter your PAN number"
                          inputProps={{
                            maxLength: 10,
                            style: { textTransform: "uppercase" },
                          }}
                          onChange={(e) => {
                            field.onChange(e.target.value.toUpperCase());
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <FormControl
                      fullWidth
                      error={Boolean(errors.donorReferralSource)}
                    >
                      <Controller
                        name="donorReferralSource"
                        control={control}
                        rules={{ required: "Referral source is required" }}
                        render={({ field }) => (
                          <SelectAutoComplete
                            size="small"
                            labelId="referral-source-label"
                            label="Referral Source"
                            nameArray={referralSourceOptionsData}
                            value={selectedReferralSourceId}
                            onChange={(e) => {
                              field.onChange(e);
                              handleReferralSourceChange(e);
                            }}
                          />
                        )}
                      />
                      {errors.donorReferralSource && (
                        <FormHelperText sx={{ color: "error.main" }}>
                          {errors.donorReferralSource.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  {referralSourceRegisteredName === "Any Other" && (
                    <Grid item xs={12}>
                      <Controller
                        name="donorReferralSourceAnyOther"
                        control={control}
                        rules={{
                          required: "Referral Source is required",
                          maxLength: {
                            value: 100,
                            message:
                              "Referral Source cannot exceed 50 characters",
                          },
                        }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Any Other Referral Source*"
                            placeholder="Enter Any Other Referral Source"
                            fullWidth
                            size="small"
                            error={Boolean(errors.donorReferralSourceAnyOther)}
                            helperText={
                              errors.donorReferralSourceAnyOther?.message
                            }
                            inputProps={{ maxLength: 100 }}
                          />
                        )}
                      />
                    </Grid>
                  )}
                </>
              ) : (
                <>
                  <Grid item xs={12} sx={{ marginBottom: 2 }}>
                    <Controller
                      name="trustName"
                      control={control}
                      rules={{ required: "Trust Name is required" }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Trust Name*"
                          placeholder="Enter Trust Name"
                          fullWidth
                          size="small"
                          onChange={(e) => {
                            const newValue = e.target.value.replace(/\s/g, " ");
                            field.onChange(newValue);
                          }}
                          id="trust-name"
                          error={Boolean(errors.trustName)}
                          helperText={errors.trustName?.message}
                          InputLabelProps={{
                            shrink: true,
                            sx: { fontSize: "1rem" },
                          }}
                          sx={{
                            borderRadius: "5px",
                            background: "white",
                            width: "100%",
                          }}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} sx={{ marginBottom: 2 }}>
                    <Controller
                      name="contactPersonName"
                      control={control}
                      rules={{ required: "Contact Person Name is required" }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Contact Person Name*"
                          placeholder="Enter Contact Person Name"
                          fullWidth
                          size="small"
                          value={name}
                          onChange={(e) => {
                            const newValue = e.target.value.replace(/\s/g, " ");
                            field.onChange(newValue);
                            setName(newValue);
                          }}
                          id="contact-name"
                          error={Boolean(errors.contactPersonName)}
                          helperText={errors.contactPersonName?.message}
                          InputLabelProps={{
                            shrink: true,
                            sx: { fontSize: "1rem" },
                          }}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} sx={{ marginBottom: 2 }}>
                    <Controller
                      name="email"
                      control={control}
                      rules={{
                        required: "Email is required",
                        pattern: {
                          value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                          message: "Enter a valid email address",
                        },
                        minLength: {
                          value: 8,
                          message: "Email must be at least 8 characters long",
                        },
                        maxLength: {
                          value: 100,
                          message: "Email cannot be longer than 100 characters",
                        },
                        validate: async (value) => {
                          try {
                            const res = await axios.post(
                              getUrl(authConfig.individualVerificationAudit) +
                                "/check-email",
                              { email: value }
                            );
                            if (res?.data?.message === "Email Already Exist") {
                              return "Email Already Exist";
                            }
                            return true;
                          } catch (err) {
                            return "Failed to validate email";
                          }
                        },
                      }}
                      render={({ field }) => (
                        <TextField
                          type="email"
                          {...field}
                          label="Contact Person Email*"
                          size="small"
                          InputLabelProps={{
                            shrink: true,
                            sx: { fontSize: "1rem" },
                          }}
                          helperText={errors.email?.message}
                          error={Boolean(errors.email)}
                          placeholder="Enter your email"
                          fullWidth
                          inputProps={{
                            minLength: 8,
                            maxLength: 100,
                          }}
                          onChange={(event) => {
                            if (event.target.value.length <= 100) {
                              field.onChange(event);
                              // setEmail(event.target.value);
                            }
                          }}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} sx={{ marginBottom: 2 }}>
                    <FormControl sx={{ width: "100%" }}>
                      <Box display="flex" alignItems="center" width="100%">
                        <Controller
                          name="mobileNumber"
                          control={control}
                          rules={{ required: "Mobile number is required" }}
                          render={({ field }) => (
                            <MobileNumberValidation
                              {...field}
                              label="Mobile Number*"
                              size="small"
                              InputLabelProps={{ shrink: true }}
                              value={mobileNumber}
                              onChange={(event) => {
                                 field.onChange(event); 
                                setMobileNumber(event);
                              }}
                              helperText={errors.mobileNumber?.message}
                              error={Boolean(errors.mobileNumber)}
                              placeholder="Enter your mobile number"
                              fullWidth
                              disabled={mobileVerified}
                              sx={{
                                "& .MuiInputBase-input::placeholder": {
                                  fontSize: "1rem",
                                  "@media (max-width:600px)": {
                                    fontSize: "0.75rem",
                                  },
                                },
                                "& .MuiInputBase-input": {
                                  padding: "8px",
                                  fontSize: "1rem",
                                  "@media (max-width:600px)": {
                                    padding: "6px",
                                    fontSize: "0.75rem",
                                  },
                                },
                                "& .MuiInputLabel-root": {
                                  fontSize: "1.0rem",
                                  "@media (max-width:600px)": {
                                    fontSize: "0.75rem",
                                  },
                                },
                              }}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="end">
                                    {mobileVerified ? (
                                      <CustomChip
                                        rounded={true}
                                        skin="light"
                                        size="small"
                                        label={
                                          <span style={{ fontSize: "inherit" }}>
                                            Verified
                                          </span>
                                        }
                                        color="success"
                                        sx={{
                                          textTransform: "capitalize",
                                          mr: { xs: 0, lg: 0 },
                                          fontSize: {
                                            xs: "0.7rem",
                                            lg: "0.8rem",
                                          },
                                        }}
                                      />
                                    ) : (
                                      <Box
                                        onClick={() => {
                                          if (!isDisabled) {
                                            handleVerifyClick(field.value);
                                          }
                                        }}
                                        sx={{
                                          border: "1px solid #f2f7f2",
                                          borderRadius: "4px",
                                          padding: "0 5px",
                                          cursor: isDisabled
                                            ? "not-allowed"
                                            : "pointer",
                                          color: isDisabled ? "gray" : "blue",
                                          opacity: isDisabled ? 0.6 : 1,
                                          pointerEvents: isDisabled
                                            ? "none"
                                            : "auto",
                                        }}
                                      >
                                        <Typography
                                          variant="caption"
                                          sx={{
                                            fontSize: {
                                              xs: "0.7rem",
                                              lg: "0.8rem",
                                            },
                                            color: isDisabled ? "gray" : "blue",
                                          }}
                                        >
                                          {loadingOtp ? (
                                            <CircularProgress
                                              color="inherit"
                                              size={22}
                                            />
                                          ) : (
                                            "Verify"
                                          )}
                                        </Typography>
                                      </Box>
                                    )}
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </Box>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sx={{ marginBottom: 2 }}>
                    <FormControl fullWidth>
                      <Controller
                        name="website"
                        control={control}
                        rules={{ pattern: /^(ftp|http|https):\/\/[^ "]+$/ }}
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            type="url"
                            value={value}
                            size="small"
                            label="Website"
                            onChange={onChange}
                            error={Boolean(errors.website)}
                            placeholder="https://www.example.com"
                            aria-describedby="validation-website"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sx={{ marginBottom: 2 }}>
                    <Controller
                      name="orgEmail"
                      control={control}
                      rules={{
                        required: "Email is required",
                        pattern: {
                          value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                          message: "Enter a valid email address",
                        },
                        minLength: {
                          value: 8,
                          message: "Email must be at least 8 characters long",
                        },
                        maxLength: {
                          value: 100,
                          message: "Email cannot be longer than 100 characters",
                        },
                      }}
                      render={({ field }) => (
                        <TextField
                          type="email"
                          {...field}
                          label="Organisation Email*"
                          size="small"
                          InputLabelProps={{
                            shrink: true,
                            sx: { fontSize: "1rem" },
                          }}
                          helperText={errors.orgEmail?.message}
                          error={Boolean(errors.orgEmail)}
                          placeholder="organisation email cannot be changed later"
                          fullWidth
                          inputProps={{
                            minLength: 8,
                            maxLength: 100,
                          }}
                          onChange={(event) => {
                            if (event.target.value.length <= 100) {
                              field.onChange(event);
                              // setEmail(event.target.value);
                            }
                          }}
                        />
                      )}
                    />
                  </Grid>
                </>
              )}
            </Grid>
          </Box>
          <Box
            sx={{
              display: "flex",
              flexWrap: "wrap",
              mt: 3,
            }}
          >
            <Typography
              sx={{
                color: "text.secondary",
                mr: 2,
                fontSize: { xs: "0.7rem", lg: "0.9rem" },
              }}
            >
              Already have an account?{" "}
            </Typography>
            <Typography
              sx={{
                color: "text.secondary",
                fontSize: { xs: "0.7rem", lg: "0.9rem" },
              }}
            >
              <LinkStyled
                href="/login"
                sx={{
                  fontWeight: 500,
                  fontSize: { xs: "0.7rem", lg: "0.9rem" },
                }}
              >
                Login
              </LinkStyled>
            </Typography>
          </Box>
        </Box>

        <Box
          sx={{
            width: "80%",
            py: 2,
            textAlign: "center",
            mt: "auto",
          }}
        >
          <>
            <Button
              variant="contained"
              color="primary"
              sx={{ mt: 4, width: "100%" }}
              onClick={handleSubmit(submit)}
              disabled={!mobileVerified}
            >
              {loading ? (
                <CircularProgress color="inherit" size={24} />
              ) : (
                "Sign Up"
              )}
            </Button>
          </>
        </Box>
      </Container>
      <Snackbar
        open={showToast}
        autoHideDuration={6000}
        onClose={handleToastClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleToastClose}
          severity="success"
          sx={{ width: "100%" }}
        >
          {toastMessage}
        </Alert>
      </Snackbar>
      <OtpDialog
        open={showOtpDialog}
        onClose={() => setShowOtpDialog(false)}
        contact={verifyingContact}
        onValidate={handleValidateOtp}
        onResend={handleResendOtp}
        countdown={countdown}
        setOtpValue={setOtpValue}
        otpValue={otpValue}
        loading={loadingOtp}
      />
    </>
  );
};

SignupPage.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
SignupPage.guestGuard = true;

export default SignupPage;
