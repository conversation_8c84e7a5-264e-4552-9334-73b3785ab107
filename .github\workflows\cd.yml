
name: Complete CD Pipeline

on:
  push:
    branches: [main]

jobs:
  test_and_build:
    uses: ./.github/workflows/npm-build-and-test.yml

  sonar:
    needs: test_and_build
    uses: ./.github/workflows/sonarqube-analysis.yml
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  semgrep:
    needs: sonar
    uses: ./.github/workflows/semgrep-scan.yml
    secrets:
      DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
      KUBERNETES_CLUSTER_ID: ${{ secrets.KUBERNETES_CLUSTER_ID }}
      KUBERNETES_CONTEXT: ${{ secrets.KUBERNETES_CONTEXT }}
      SECURE_GITHUB_TOKEN: ${{ secrets.SECURE_GITHUB_TOKEN }}

  docker:
    needs: semgrep
    uses: ./.github/workflows/docker-push.yml
    secrets:
      DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

  gitops-deploy:
    needs: docker
    uses: ./.github/workflows/deploy-gitops.yml
    secrets:
      GITOPS_TOKEN: ${{ secrets.GITOPS_TOKEN }}